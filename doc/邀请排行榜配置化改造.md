# 邀请排行榜配置化改造

## 修改概述

**修改时间：** 2025-01-29  
**修改目的：** 将邀请排行榜中的排除用户前缀从硬编码改为配置文件管理，支持完全配置驱动  
**影响模块：** 活动系统 - 邀请排行榜功能  
**配置化内容：** 用户名前缀排除列表

## 需求背景

在之前的实现中，邀请排行榜需要排除的用户名前缀是硬编码在代码中的。为了提高系统的灵活性和可维护性，需要将这些配置项移到配置文件中管理，并且支持以下场景：

- **有配置时**：按配置的前缀进行用户过滤
- **无配置时**：不进行任何过滤，显示所有用户的邀请排行

## 技术方案

采用**完全配置驱动**的方案：
- 在`application.yml`中定义可选的配置项
- 创建专门的配置属性类
- 根据配置是否存在选择不同的查询策略
- 移除所有硬编码的默认值

## 具体修改内容

### 1. 配置文件修改

**文件：** `ruoyi-admin/src/main/resources/application.yml`

**新增配置：**
```yaml
# 项目相关配置
ruoyi:
  # ... 其他配置
  # 活动相关配置
  activity:
    # 邀请排行榜需要排除的用户名前缀（可选配置）
    invite-rank:
      exclude-prefixes:
        - "1700"
        - "1701"
        - "1702"
        - "162"
        - "1703"
        - "1705"
        - "1706"
        - "165"
        - "1704"
        - "1707"
        - "1708"
        - "1709"
        - "171"
        - "167"
```

**注意：** 如果不需要过滤任何用户，可以完全删除这个配置项或将`exclude-prefixes`设为空数组。

### 2. 创建配置属性类

**文件：** `asset-issuance/src/main/java/com/ruoyi/issue/common/config/ActivityConfig.java`

```java
package com.ruoyi.issue.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 活动相关配置属性
 * 
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@Component
@ConfigurationProperties(prefix = "ruoyi.activity")
public class ActivityConfig {

    /**
     * 邀请排行榜配置
     */
    private InviteRank inviteRank = new InviteRank();

    /**
     * 邀请排行榜配置类
     */
    @Data
    public static class InviteRank {
        /**
         * 需要排除的用户名前缀列表（可选）
         */
        private List<String> excludePrefixes;
    }
}
```

### 3. 修改Service实现

**文件：** `asset-issuance/src/main/java/com/ruoyi/issue/activity/service/impl/DigActivityServiceImpl.java`

**修改内容：**

1. 添加依赖注入：
```java
private final ActivityConfig activityConfig;
```

2. 修改`getInviteRank`方法：
```java
@Override
public List<ActivityRankVo> getInviteRank(Long activityId) {
    DigActivity activity = digActivityMapper.selectDigActivityByActivityId(activityId);
    
    // 从配置文件中获取需要排除的用户名前缀
    List<String> excludePrefixes = activityConfig.getInviteRank().getExcludePrefixes();
    
    List<ActivityRankVo> inviteRank;
    
    // 如果配置为空，不进行排除，使用原始查询方法
    if (excludePrefixes == null || excludePrefixes.isEmpty()) {
        log.info("邀请排行榜排除前缀配置为空，不进行用户过滤");
        inviteRank = digUserInvitationMapper.getInviteRank(
            activity.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), 
            activity.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
        );
    } else {
        log.info("邀请排行榜使用配置的排除前缀进行过滤，排除前缀数量: {}", excludePrefixes.size());
        inviteRank = digUserInvitationMapper.getInviteRankWithUserFilter(
            activity.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), 
            activity.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            excludePrefixes
        );
    }
    
    // 数据处理逻辑保持不变
    inviteRank.forEach(item -> {
        SysUser sysUser = sysUserMapper.selectUserById(item.getUserId());
        item.setNickName(DataDesensitizationUtil.replaceLeftRight(sysUser.getUserName(), 3, 4));
        if (StringUtils.isNotBlank(sysUser.getAvatar())) {
            item.setAvatar(attachmentInfoService.getObjectUrl(sysUser.getAvatar()));
        }
    });
    return inviteRank;
}
```

## 功能特性

### 1. 完全配置驱动
- **有配置时**：使用`getInviteRankWithUserFilter`方法进行过滤查询
- **无配置时**：使用原始的`getInviteRank`方法，不进行任何过滤
- **灵活控制**：可以通过配置文件完全控制过滤行为

### 2. 智能查询策略
- **性能优化**：无配置时避免不必要的JOIN查询
- **日志记录**：清晰记录当前使用的查询策略
- **向后兼容**：完全兼容原有的查询逻辑

### 3. 配置管理灵活性
- **可选配置**：配置项完全可选，不配置即不过滤
- **动态调整**：可以随时通过配置文件调整过滤规则
- **环境隔离**：不同环境可以使用不同的过滤策略

## 使用场景

### 场景1：需要过滤特定用户
```yaml
ruoyi:
  activity:
    invite-rank:
      exclude-prefixes:
        - "test"
        - "admin"
        - "1700"
```

### 场景2：不需要过滤任何用户
```yaml
# 方式1：完全不配置activity节点
ruoyi:
  name: RuoYi
  # ... 其他配置

# 方式2：配置空数组
ruoyi:
  activity:
    invite-rank:
      exclude-prefixes: []

# 方式3：不配置exclude-prefixes
ruoyi:
  activity:
    invite-rank: {}
```

## 测试验证

### 1. 有配置的情况
- 配置排除前缀
- 验证过滤查询正常工作
- 验证日志输出正确

### 2. 无配置的情况
- 删除或注释配置项
- 验证使用原始查询方法
- 验证所有用户都出现在排行榜中

### 3. 配置变更测试
- 动态修改配置文件
- 重启应用验证配置生效
- 验证查询策略正确切换

## 优势总结

| 特性 | 说明 | 优势 |
|------|------|------|
| 配置驱动 | 完全由配置控制过滤行为 | 灵活性最大化 |
| 性能优化 | 无配置时避免复杂查询 | 提升查询性能 |
| 零默认值 | 移除所有硬编码默认值 | 配置更清晰 |
| 向后兼容 | 保持原有查询逻辑 | 升级无风险 |
| 环境适配 | 支持不同环境不同策略 | 部署更灵活 |

## 注意事项

1. **配置格式**：确保YAML格式正确，注意缩进
2. **重启生效**：配置修改后需要重启应用
3. **日志监控**：关注日志输出确认使用的查询策略
4. **性能考虑**：有配置时会使用JOIN查询，注意数据库性能
5. **测试覆盖**：确保两种查询策略都有对应的测试用例

## 版本记录

| 版本 | 修改时间 | 修改人 | 修改内容 |
|------|----------|--------|----------|
| v1.0 | 2025-01-29 | 系统 | 初始实现配置化改造 |
| v1.1 | 2025-01-29 | 系统 | 移除默认值，支持完全配置驱动 |

---

**文档维护：** 请在后续相关修改时及时更新本文档
