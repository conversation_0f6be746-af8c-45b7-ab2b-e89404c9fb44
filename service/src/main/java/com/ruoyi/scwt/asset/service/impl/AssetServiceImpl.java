package com.ruoyi.scwt.asset.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scwt.asset.constant.AssetConstants;
import com.ruoyi.scwt.asset.dto.AssetDto;
import com.ruoyi.scwt.asset.dto.AssetStatusDto;
import com.ruoyi.scwt.asset.entity.Asset;
import com.ruoyi.scwt.asset.entity.AssetZoneShop;
import com.ruoyi.scwt.asset.mapper.AssetMapper;
import com.ruoyi.scwt.asset.service.AssetService;
import com.ruoyi.scwt.asset.service.AssetZoneShopService;
import com.ruoyi.scwt.asset.vo.AssetVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
@Service
public class AssetServiceImpl extends ServiceImpl<AssetMapper, Asset> implements AssetService {

    @Autowired
    private AssetZoneShopService assetZoneShopService;

    @Override
    public Long getAssetTypeNumByShopId(Long shopId,String transactionType) {
        List<AssetZoneShop> assetZoneShopList = assetZoneShopService.getListByShopId(shopId);
        if (assetZoneShopList.isEmpty()){
            return 0L;
        }
        List<Long> idList = assetZoneShopList.stream().map(AssetZoneShop::getAssetId).collect(Collectors.toList());
        Long num = baseMapper.selectCount(Wrappers.<Asset>lambdaQuery()
                .eq(Asset::getTransactionType, transactionType)
                .eq(Asset::getStatusCd, AssetConstants.ASSET_ON_SHELF)
                .in(Asset::getAssetId, idList));
        return num;
    }

    @Override
    public Long getForSaleNumByShopId(Long shopId) {
        List<AssetZoneShop> assetZoneShopList = assetZoneShopService.getListByShopId(shopId);
        if (assetZoneShopList.isEmpty()){
            return 0L;
        }
        List<Long> idList = assetZoneShopList.stream().map(AssetZoneShop::getAssetId).collect(Collectors.toList());
        Long num = baseMapper.selectCount(Wrappers.<Asset>lambdaQuery()
                .eq(Asset::getStatusCd, AssetConstants.ASSET_ON_SHELF)
                .in(Asset::getAssetId, idList));
        return num;
    }

    @Override
    public String updateStatus(AssetStatusDto dto , Long userId) {
        Asset asset = baseMapper.selectById(dto.getAssetId());
        if (SecurityUtils.isManager() || userId.toString().equals(asset.getCreateStaff())){
            asset.setStatusCd(dto.getStatusCd());
            asset.setStatusDate(LocalDateTime.now());
            asset.setDownReason(dto.getDownReason());
            asset.updateById();
            return "";
        }else {
            return "无权限操作";
        }
    }

    @Override
    public void setShopStatusCd(Long shopId,String statusCd,String remark) {
        List<AssetZoneShop> assetZoneShops = assetZoneShopService.getListByShopId(shopId);
        assetZoneShops.forEach(item->{
            baseMapper.update(null,Wrappers.<Asset>lambdaUpdate().eq(Asset::getAssetId,item.getAssetId()).set(Asset::getShopStatus,statusCd).set(StringUtils.isNotEmpty(remark),Asset::getRemark,remark));
        });
    }

}
