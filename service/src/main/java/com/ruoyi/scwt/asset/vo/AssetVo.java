package com.ruoyi.scwt.asset.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.scwt.common.entity.SysLabel;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.shop.vo.ShopInfoVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.poi.hpsf.Decimal;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


@Data
public class AssetVo  {

  @ApiModelProperty(value = "资产ID")
  private Long assetId;

  @ApiModelProperty(value = "用户ID")
  private Long userId;

  @ApiModelProperty(value = "资产来源")
  private String assetSource;

  @ApiModelProperty(value = "资产名称")
  private String assetName;

  @ApiModelProperty(value = "资产封面")
  private String assetCover;

  @ApiModelProperty(value = "资产类型")
  private String assetType;

  @ApiModelProperty(value = "资产业务类型")
  private String businessCategory;

  @ApiModelProperty(value = "资产简介")
  private String assetAbstract;

  @ApiModelProperty(value = "资产交易类型")
  private String transactionType;

  @ApiModelProperty(value = "授权类型")
  private String authorizationType;

  @ApiModelProperty(value = "授权地区")
  private String authorizationArea;

  @ApiModelProperty(value = "资产文件ID，多个以逗号隔开")
  private String assetFileIds;

  @ApiModelProperty(value = "个人授权价格")
  private BigDecimal personalPrice;

  @ApiModelProperty(value = "企业授权价格")
  private BigDecimal enterprisePrice;

  @ApiModelProperty(value = "转让价格")
  private BigDecimal transferPrice;

  @ApiModelProperty(value = "分辨率")
  private String resolvingPower;

  @ApiModelProperty(value = "文件大小")
  private String fileSize;

  @ApiModelProperty(value = "码率")
  private String bitRate;

  @ApiModelProperty(value = "时长")
  private String duration;

  @ApiModelProperty(value = "像素")
  private String pixel;

  @ApiModelProperty(value = "版权证明材料")
  private String CopyrightMaterials;

  @ApiModelProperty(value = "权利，多个以逗号隔开")
  private String rights;

  @ApiModelProperty(value = "期限（天）")
  private int days;
  @ApiModelProperty(value = "备注内容")
  private String rightRemark;
  @ApiModelProperty(value = "交易价格")
  private String transactionPrice;

  @ApiModelProperty(value = "协议文件")
  private String agreementDoc;
  @ApiModelProperty(hidden = true)
  private LocalDateTime createDate;
  @ApiModelProperty(hidden = true)
  private String statusCd;
  @ApiModelProperty(hidden = true)
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  private LocalDateTime statusDate;
  @ApiModelProperty(value="标签")
  private String labelName;
  @ApiModelProperty(value="店铺信息")
  private ShopInfoVo shopInfoVo;
  @ApiModelProperty(value="文件列表")
  private List<AttachmentInfo> fileList;
  @ApiModelProperty(value = "下架原因")
  private String downReason;
  @ApiModelProperty(value = "近30天成交数量")
  private String thirtyDealNum;

  @ApiModelProperty(hidden = true)
  @TableField(exist = false)
  private String belongUser;
}
