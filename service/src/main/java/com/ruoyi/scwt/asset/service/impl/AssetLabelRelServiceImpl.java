package com.ruoyi.scwt.asset.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.scwt.asset.entity.AssetLabelRel;
import com.ruoyi.scwt.asset.entity.AssetZoneShop;
import com.ruoyi.scwt.asset.mapper.AssetLabelRelMapper;
import com.ruoyi.scwt.asset.mapper.AssetZoneShopMapper;
import com.ruoyi.scwt.asset.service.AssetLabelRelService;
import com.ruoyi.scwt.asset.service.AssetZoneShopService;
import com.ruoyi.scwt.shop.entity.ShopLabelRel;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
@Service
public class AssetLabelRelServiceImpl extends ServiceImpl<AssetLabelRelMapper, AssetLabelRel> implements AssetLabelRelService {


    @Override
    public void insertLabelRel(Long assetId, String labelIds) {
        String[] labelIdList = labelIds.split(",");
        for (String labelId : labelIdList) {
            AssetLabelRel assetLabelRel = new AssetLabelRel();
            assetLabelRel.setAssetId(assetId);
            assetLabelRel.setLabelId(Long.valueOf(labelId));
            this.save(assetLabelRel);
        }
    }
}
