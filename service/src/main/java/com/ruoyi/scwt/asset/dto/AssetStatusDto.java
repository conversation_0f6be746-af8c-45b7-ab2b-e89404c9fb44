package com.ruoyi.scwt.asset.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
public class AssetStatusDto {

  /**
   * 资产ID
   */
  @ApiModelProperty(value = "资产ID")
  private Long assetId;

  /**
   * 状态
   */
  @ApiModelProperty(value = "状态")
  private String statusCd;
  @ApiModelProperty(value = "下架原因")
  private String downReason;

  public AssetStatusDto(Long assetId, String statusCd,String downReason) {
    this.assetId = assetId;
    this.statusCd = statusCd;
    this.downReason = downReason;
  }

  public AssetStatusDto() {
  }
}
