package com.ruoyi.scwt.asset.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;


@Data
@ApiModel
@TableName("asset_audit_records")
@EqualsAndHashCode(callSuper = true)
public class AssetAuditRecords extends Model<AssetAuditRecords> {
private static final long serialVersionUID = 1L;

  /**
   * ID
   */
  @TableId(type= IdType.AUTO)
  @ApiModelProperty(value = "ID",hidden = true)
  private Long id;
  /**
   * 资产ID
   */
//  @NotBlank(message = "资产ID不能为空")
  @ApiModelProperty(value = "资产ID")
  private Long assetId;

  /**
   * 审核结果
   */
//  @NotBlank(message = "审核结果不能为空")
  @ApiModelProperty(value = "审核结果")
  private String auditResult;

  /**
   * 结果描述
   */
//  @NotBlank(message = "结果描述不能为空")
  @ApiModelProperty(value = "结果描述")
  private String auditDescribe;

  /**
   * 审核员
   */
  @ApiModelProperty(value = "审核员")
  private String createStaff;

  /**
   * 审核时间
   */
  @ApiModelProperty(value = "审核时间")
  private LocalDateTime createDate;;


}
