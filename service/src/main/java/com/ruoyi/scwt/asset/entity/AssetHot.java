package com.ruoyi.scwt.asset.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


@Data
@ApiModel
@TableName("asset_hot")
@EqualsAndHashCode(callSuper = true)
public class AssetHot extends Model<AssetHot> {
private static final long serialVersionUID = 1L;

  /**
   * 资产ID
   */
  @ApiModelProperty(value = "资产ID")
  private Long assetId;

  /**
   * 标签ID
   */
  @ApiModelProperty(value = "排序")
  private Long sort;
  @ApiModelProperty(hidden = true)
  private LocalDateTime createDate;

}
