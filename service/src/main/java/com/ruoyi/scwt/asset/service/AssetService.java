package com.ruoyi.scwt.asset.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.scwt.asset.dto.AssetDto;
import com.ruoyi.scwt.asset.dto.AssetStatusDto;
import com.ruoyi.scwt.asset.entity.Asset;
import com.ruoyi.scwt.asset.vo.AssetVo;
import com.ruoyi.scwt.identify.entity.Identify;

import java.util.List;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
public interface AssetService extends IService<Asset> {


    Long getAssetTypeNumByShopId(Long shopId,String transactionType);

    Long getForSaleNumByShopId(Long shopId);

    String updateStatus(AssetStatusDto dto,Long userId);

    void setShopStatusCd(Long shopId, String statusCd,String remark);
}
