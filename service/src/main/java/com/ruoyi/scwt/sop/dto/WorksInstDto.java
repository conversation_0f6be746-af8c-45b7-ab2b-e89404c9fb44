package com.ruoyi.scwt.sop.dto;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 登记作品
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Data
@ApiModel(value = "作品表")
public class WorksInstDto extends Model<WorksInstDto> {
private static final long serialVersionUID = 1L;

    /**
     * 作品ID，主键
     */
//    @TableId
    @ApiModelProperty(value="作品ID")
    private Long worksId;

    @ApiModelProperty(value="受理号",hidden = true)
    private String acceptanceNumber;
    /**
     * 作品名称
     */
    @ApiModelProperty(value="作品名称",required = true)
    @NotBlank(message = "作品名称不能为空")
    private String worksName;
    /**
     * 作品类型   worksInstWorksType
     */
    @ApiModelProperty(value="作品类型",required = true)
    @NotBlank(message = "作品类型不能为空")
    private String worksType;
    /**
     * 作者姓名或名称
     */
    @ApiModelProperty(value="作者姓名或名称",required = true)
    @NotBlank(message = "作者姓名或名称不能为空")
    private String authorName;
    /**
     * 作者署名
     */
    @ApiModelProperty(value="作者署名",required = true)
    @NotBlank(message = "作者署名不能为空")
    private String authorSign;
    /**
     * 样本文件ID，多个以逗号隔开
     */
    @ApiModelProperty(value="样本文件ID，多个以逗号隔开",required = true)
    @NotBlank(message = "样本文件ID不能为空")
    private String sampleFileId;
    /**
     * 权力保证书ID
     */
    @ApiModelProperty(value="权力保证书ID",required = true)
    @NotBlank(message = "权力保证书ID不能为空")
    private String powerGuaranteeId;
    /**
     * 其他证明材料ID，多个以逗号隔开
     */
    @ApiModelProperty(value="其他证明材料ID，多个以逗号隔开")
    private String OtherMaterialsId;

    @ApiModelProperty(value="著作权人id，多个以逗号隔开",required = true)
    @NotEmpty(message = "著作权人id不能为空，多个以逗号隔开")
    private String ownersIds;
    /**
     * 作品封面
     */
    @ApiModelProperty(value="作品封面")
    private String worksCover;
    /**
     * 创作性质   worksInstCreateNature
     */
    @ApiModelProperty(value="创作性质",required = true)
    @NotBlank(message = "创作性质不能为空")
    private String inditeNature;
    /**
     * 权利拥有方式     rightOwnership
     */
    @ApiModelProperty(value="权利拥有方式",required = true)
    @NotBlank(message = "权利拥有方式不能为空")
    private String copyrightOwnRange;
    /**
     * 作品归属情况   worksOwnership
     */
    @ApiModelProperty(value="作品归属情况",required = true)
    @NotBlank(message = "作品归属情况不能为空")
    private String worksBelongType;
    /**
     * 权利取得方式    rightGet
     */
    @ApiModelProperty(value="权利取得方式",required = true)
    @NotBlank(message = "权利取得方式不能为空")
    private String copyrightObtainChannel;
//
//    /**
//     * 分发权利
//     */
//    @ApiModelProperty(value="分发权利",required = true)
////    @NotBlank(message = "分发权利不能为空")
//    private String copyrightDispense;
//    /**
//     * 是否区块链存证
//     */
//    @ApiModelProperty(value="是否区块链存证(0:是，1:否)",hidden = true)
//    private String isDeposited;
//    /**
//     * 作品HASH值
//     */
//    @ApiModelProperty(value="作品HASH值",hidden = true)
//    private String worksHash;
//    /**
//     * 时间戳ID
//     */
//    @ApiModelProperty(value="时间戳ID",hidden = true)
//    private Long tsaId;
//    /**
//     * 天平链ID
//     */
//    @ApiModelProperty(value="天平链ID",hidden = true)
//    private Long balanceId;
//    /**
//     * 作品上链id
//     */
//    @ApiModelProperty(value="作品上链id",hidden = true)
//    private Long blockchainId;
//    /**
//     * 作品上链hash
//     */
//    @ApiModelProperty(value="作品上链hash",hidden = true)
//    private String blockchainHash;
//    /**
//     * 作品上链时间
//     */
//    @ApiModelProperty(value="作品上链时间",hidden = true)
//    private LocalDateTime blockchainTime;
//    /**
//     * 存证证书地址
//     */
//    @ApiModelProperty(value="存证证书地址",hidden = true)
//    private String certificateUrl;
//    /**
//     * 登记证书地址
//     */
//    @ApiModelProperty(value="登记证书地址",hidden = true)
//    private String RegistrationCertificateUrl;
//    /**
//     * 作品简介
//     */
//    @ApiModelProperty(value="作品简介",required = true)
//    @NotBlank(message = "作品简介不能为空")
//    private String worksAbstract;
//    /**
//     * 作品创意
//     */
//    @ApiModelProperty(value="作品创意",required = true)
//    @NotBlank(message = "作品创意不能为空")
//    private String worksCreativity;

//    /**
//     * 内容简介
//     */
//    @ApiModelProperty(value="内容简介",required = true)
//    private String contentAbstract;
//
//    /**
//     * 创作经过
//     */
//    @ApiModelProperty(value="创作经过",required = true)
//    @NotBlank(message = "创作经过不能为空")
//    private String worksCreatProcess;
    /**
     * 作品创作说明
     */
    @ApiModelProperty(value="作品创作说明",required = true)
    @NotNull(message = "作品创作说明不能为空")
    private String description;
    /**
     * 创作完成时间
     */
    @ApiModelProperty(value="创作完成时间",required = true)
    @NotNull(message = "创作完成时间不能为空")
    private String inditeDoneTime;
    /**
     * 创作完成地点
     */
    @ApiModelProperty(value="创作完成地点",required = true)
    @NotNull(message = "创作完成地点能为空")
    private String createPlace;
    /**
     * 首次发表日期
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value="首次发表日期")
    private String publishDate;
    /**
     * 发表地点
     */
    @ApiModelProperty(value="发表地点")
    private String publishPlace;
    /**
     * 审核意见
     */
//    @ApiModelProperty(value="审核意见",hidden = true)
//    @NotBlank(message = "审核意见")
//    private String auditOpinion;
//
//    @ApiModelProperty(value="省版权局审核意见",hidden = true)
//    private String scAuditOpinion;
//    /**
//     * 获证时间
//     */
//    @TableField(updateStrategy = FieldStrategy.IGNORED)
//    @ApiModelProperty(value="获证时间",hidden = true)
//    private LocalDateTime obtainedTime;
//
//    @ApiModelProperty(value="省版权局证书编号",hidden = true)
//    private String cerNo;

    /**
     * 作品状态
     */
    @ApiModelProperty(value="作品状态",hidden = true)
    private String statusCd;
    /**
//     * 状态时间
//     */
//    @ApiModelProperty(value="状态时间",hidden = true)
//    private LocalDateTime statusDate;
//    /**
//     * 创建人
//     */
//    @ApiModelProperty(value="创建人",hidden = true)
//    @TableField(fill = FieldFill.INSERT)
//    private String createStaff;
//    /**
//     * 创建时间
//     */
//    @ApiModelProperty(value="创建时间",hidden = true)
//    @TableField(fill = FieldFill.INSERT)
//    private LocalDateTime createDate;
//    /**
//     * 修改人
//     */
//    @ApiModelProperty(value="修改人",hidden = true)
//    @TableField(fill = FieldFill.UPDATE)
//    private String updateStaff;
//    /**
//     * 修改时间
//     */
//    @ApiModelProperty(value="修改时间",hidden = true)
//    @TableField(fill = FieldFill.UPDATE)
//    private LocalDateTime updateDate;
//    /**
//     * 备注
//     */
//    @ApiModelProperty(value="备注",hidden = true)
//    private String remark;


//    @TableField(exist = false)
    @ApiModelProperty(value = "平台用户主键id", required = true)
    @NotBlank(message = "平台用户主键id能为空")
    private String platformUserId;

    }
