package com.ruoyi.scwt.sop.task;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.scwt.common.util.AESUtils;
import com.ruoyi.scwt.identify.constant.IdentifyConstants;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.entify.Union;
import com.ruoyi.scwt.pay.service.UnionService;
import com.ruoyi.scwt.sop.DataProcessing.Bmark2Sop;
import com.ruoyi.scwt.works.constant.OwnerConstants;
import com.ruoyi.scwt.works.constant.WorksConstants;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.entity.RegisterOwner;
import com.ruoyi.scwt.works.entity.WorksEvidence;
import com.ruoyi.scwt.works.entity.WorksRegister;
import com.ruoyi.scwt.works.service.EvidenceOwnerService;
import com.ruoyi.scwt.works.service.RegisterOwnerService;
import com.ruoyi.scwt.works.service.WorksEvidenceService;
import com.ruoyi.scwt.works.service.WorksRegisterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
@Async
public class WorksScheduledTask {


    private final EvidenceOwnerService evidenceOwnerService;
    private final RegisterOwnerService registerOwnerService;
    private final WorksRegisterService worksRegisterService;
    private final WorksEvidenceService worksEvidenceService;
    private final IdentifyService identifyService;
    private final UnionService unionService;

    /***
     * 定时查询登记作品存证信息
     */
//    @Scheduled(fixedDelay  = 1000 * 60 * 30) //每隔30分钟执行一次
//    public void getWorksRegisterDataForEvidence() {
//        log.info("======轮询查询登记作品存证信息定时任务开启======");
//        List<WorksInst> list = worksInstService.list(Wrappers.<WorksInst>lambdaQuery().isNotNull(WorksInst::getAcceptanceNumber).like(WorksInst::getAcceptanceNumber,"SOP").eq(WorksInst::getStatusCd, WorksStatus.WORKS_STATUS_1200.getStatus()));
//        log.info("本次获取作品数为：{}条", list.size());
//        if (ObjectUtil.isNotEmpty(list)) {
//            list.forEach(item -> {
//                bmark2Sop.getWorkProgressByAcceptNo(new WorkByAcceptNoDto(item.getAcceptanceNumber(), item.getCreateStaff()));
//            });
//        }
//    }
//
//    /***
//     * 定时查询登记作品登记信息
//     */
//    @Scheduled(cron = "0 0 1 * * ?") //每日凌晨1点开始执行
//    public void getWorksRegisterDataForRegister() {
//        log.info("======轮询查询登记作品登记信息定时任务开启======");
//        List<WorksInst> list = worksInstService.list(Wrappers.<WorksInst>lambdaQuery().isNotNull(WorksInst::getAcceptanceNumber).like(WorksInst::getAcceptanceNumber,"SOP").eq(WorksInst::getStatusCd, WorksStatus.WORKS_STATUS_2100.getStatus()));
//        log.info("本次获取作品数为：{}条", list.size());
//        if (ObjectUtil.isNotEmpty(list)) {
//            list.forEach(item -> {
//                bmark2Sop.getWorkProgressByAcceptNo(new WorkByAcceptNoDto(item.getAcceptanceNumber(), item.getCreateStaff()));
//            });
//        }
//    }


    @Scheduled(fixedDelay  = 1000 * 60 * 30) //每隔30分钟执行一次
    public void sopForeign() {
        log.info("======轮询新增注册定时任务开启======");
        List<Identify> identifyList = identifyService.list(Wrappers.<Identify>lambdaQuery().eq(Identify::getStatusCd, IdentifyConstants.IDENTIFY_TAKE_EFFECT).isNull(Identify::getWcUserId));
        log.info("实名认证人数为：{}条", identifyList.size());
        for (Identify identify : identifyList) {
            identify.setIdNo(identify.getIdNo());
            identifyService.sopForeign(identify);
        }
    }

    @Scheduled(fixedDelay  = 1000 * 60 * 30) //每隔30分钟执行一次
    public void addEvidenceOwner() {
        log.info("======轮询新增存证著作权人定时任务开启======");
        List<EvidenceOwner> ownerList = evidenceOwnerService.list(Wrappers.<EvidenceOwner>lambdaQuery().eq(EvidenceOwner::getStatusCd, OwnerConstants.OWNER_TAKE_EFFECT).isNull(EvidenceOwner::getSopOwnerId));
        log.info("存证著作权人数为：{}条", ownerList.size());
        for (EvidenceOwner owner : ownerList) {
            evidenceOwnerService.addSopOwner(owner);
        }
    }
    @Scheduled(fixedDelay  = 1000 * 60 * 30) //每隔30分钟执行一次
    public void addRegisterOwner() {
        log.info("======轮询新增登记著作权人定时任务开启======");
        List<RegisterOwner> ownerList = registerOwnerService.list(Wrappers.<RegisterOwner>lambdaQuery().eq(RegisterOwner::getStatusCd, OwnerConstants.OWNER_TAKE_EFFECT).isNull(RegisterOwner::getSopOwnerId));
        log.info("登记著作权人数为：{}条", ownerList.size());
        for (RegisterOwner owner : ownerList) {
            registerOwnerService.addSopOwner(owner);
        }
    }

        @Scheduled(fixedDelay  = 1000 * 60 * 30) //每隔30分钟执行一次
    public void addWorksEvidence() {
        log.info("======轮询新增存证作品定时任务开启======");
        List<WorksEvidence> worksList = worksEvidenceService.list(Wrappers.<WorksEvidence>lambdaQuery().eq(WorksEvidence::getStatusCd, WorksConstants.WORKS_EVIDENCE).isNull(WorksEvidence::getSopWorksId));
        log.info("存证作品数为：{}条", worksList.size());
        for (WorksEvidence worksEvidence : worksList) {
            worksEvidenceService.addWorksEvidence(worksEvidence);
        }
    }

    @Scheduled(fixedDelay  = 1000 * 60 * 30) //每隔30分钟执行一次
    public void addWorksRegister() {
        log.info("======轮询新增登记作品定时任务开启======");
        List<WorksRegister> worksList = worksRegisterService.list(Wrappers.<WorksRegister>lambdaQuery().eq(WorksRegister::getStatusCd, WorksConstants.WORKS_REVIEW).isNull(WorksRegister::getSopWorksId));
        log.info("登记作品数为：{}条", worksList.size());
        for (WorksRegister worksRegister : worksList) {
            worksRegisterService.addWorksRegister(worksRegister);
        }
    }

    @Scheduled(fixedDelay  = 1000 * 60 * 30) //每隔30分钟执行一次
    public void getWorkProgressByAcceptNo() {
        log.info("======轮询查询登记作品审核进度定时任务开启======");
        List<WorksRegister> worksList = worksRegisterService.list(Wrappers.<WorksRegister>lambdaQuery().eq(WorksRegister::getStatusCd, WorksConstants.WORKS_REVIEW).isNotNull(WorksRegister::getSopWorksId));
        log.info("登记作品审核进度数为：{}条", worksList.size());
        for (WorksRegister worksRegister : worksList) {
            worksRegisterService.getWorkProgressByAcceptNo(worksRegister);
        }
    }

//    @Scheduled(fixedDelay  = 1000 * 60 * 30) //每隔30分钟执行一次
    public void complexApplyQry() {
        log.info("======轮询查询银联入网状态定时任务开启======");
        List<Union> unionList = unionService.list(Wrappers.<Union>lambdaQuery().eq(Union::getStatusCd, OrderConstants.UNION_AUTH_WAIT_SIGN).isNotNull(Union::getUmsRegId).isNull(Union::getApplyStatus));
        log.info("银联入网状态查询：{}条", unionList.size());
        for (Union union : unionList) {
            unionService.complexApplyQry(union);
        }
    }
}
