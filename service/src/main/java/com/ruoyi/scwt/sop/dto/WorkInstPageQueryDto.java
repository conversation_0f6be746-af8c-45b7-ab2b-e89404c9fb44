package com.ruoyi.scwt.sop.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @author：weiqian
 * @Date：2022/7/26 15:31
 */
@Data
public class WorkInstPageQueryDto {

    /**
     * 作品名称
     */
    @ApiModelProperty(value="作品名称")
    private String worksName;

    @ApiModelProperty(value = "起始时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "截止时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @ApiModelProperty(value="作品状态")
    private String statusCd;

    @ApiModelProperty(value="作品类型")
    private String worksType;

    @ApiModelProperty(value="平台用户主键id")
//    @NotBlank(message = "平台用户主键id不能为空")
    private String platformUserId;

    @ApiModelProperty(value = "每页条数")
    private long size;

    @ApiModelProperty(value = "页码")
    private long current;
}