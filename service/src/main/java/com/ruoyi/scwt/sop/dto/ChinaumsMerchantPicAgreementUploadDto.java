package com.ruoyi.scwt.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * 银联协议文件上传请求参数
 * <AUTHOR>
 * @date 2022-11-7 - 15:52
 **/
@Data
@Accessors(chain = true)
@ApiModel(value = "银联协议文件上传请求参数")
public class ChinaumsMerchantPicAgreementUploadDto {

    @ApiModelProperty(value = "请求流水号", required = true)
    @NotEmpty(message = "请求流水号不能为空")
    @Length(max = 100,message = "请求流水号长度不能超过30")
    private String requestSeq;

    @ApiModelProperty(value = "开户行帐号", required = true)
    @NotBlank(message = "开户行帐号不能为空")
    @Length(max = 40,message = "开户行帐号长度不能超过40")
    private String bankAcctNo;

    @ApiModelProperty(value = "法人身份证号", required = true)
    @NotBlank(message = "法人身份证号不能为空")
    @Length(max = 36,message = "法人身份证号长度不能超过36")
    private String legalIdcardNo;

    @ApiModelProperty(value = "法人手机号", required = true)
    @NotBlank(message = "法人手机号不能为空")
    @Length(max = 30,message = "法人手机号长度不能超过30")
    private String legalMobile;

    @ApiModelProperty(value = "法人身份证姓名", required = true)
    @NotBlank(message = "法人身份证姓名不能为空")
    @Length(max = 30,message = "法人身份证姓名长度不能超过30")
    private String legalName;
}
