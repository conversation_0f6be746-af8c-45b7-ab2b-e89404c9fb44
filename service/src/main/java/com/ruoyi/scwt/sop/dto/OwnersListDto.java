package com.ruoyi.scwt.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author：weiqian
 * @Date：2022/7/26 15:31
 */
@Data
@ApiModel(value = "著作权人列表")
public class OwnersListDto {

    @ApiModelProperty(value="平台用户ID(不传查整个平台)")
    private String platformUserId;

    @ApiModelProperty(value = "著作权人应用场景类型（存证：evidence，登记：registration）", required = true)
    @NotBlank(message = "著作权人应用场景类型不能为空")
    private String ownerSceneType;

    @ApiModelProperty(value = "每页条数")
    private long size;

    @ApiModelProperty(value = "页码")
    private long current;
}