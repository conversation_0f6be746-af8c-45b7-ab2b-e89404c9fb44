package com.ruoyi.scwt.sop.dto;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;


/**
 * 作品存证参数
 * <AUTHOR>
 * @date 2023/1/5
 **/
@Data
@ApiModel
public class FileEvidenceDto extends Model<FileEvidenceDto> {

    @ApiModelProperty(value="名称", required = true)
    @NotBlank(message = "作品名称不能为空")
    @JsonProperty("workName")
    private String workName;

    @ApiModelProperty(value="存证作品样本文件", required = true)
    @NotEmpty(message = "存证作品样本文件不能为空")
    @JsonProperty("attachIds")
    private List<Long> attachIds;

    @ApiModelProperty(value="著作权人id", required = true)
    @NotEmpty(message = "著作权人id不能为空")
    @JsonProperty("ownerIds")
    private List<Long> ownerIds;

//    @ApiModelProperty(value = "版保中心用户id", hidden = true)
//    @JsonProperty("userId")
//    private Integer userId;

    @ApiModelProperty(value = "平台用户主键id")
    @JsonProperty("platformUserId")
    private String platformUserId;

    @ApiModelProperty(value = "著作权人集合",hidden = true)
    private List<OwnerDto> ownerDtos;

    @ApiModelProperty(value = "著作权人集合",hidden = true)
    private List<AttachmentInfo> sampleFileList;
}
