package com.ruoyi.scwt.sop.dto;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 银联入网用户档案资料信息上传请求参数
 *
 * <AUTHOR>
 * @date 2022-11-7 - 15:52
 **/
@Data
@ApiModel(value = "个人-银联入网用户档案资料信息上传请求参数")
public class ChinaumsMerchantAutoRegPersonDto {

    @ApiModelProperty(value = "调用接口名称", hidden = true)
    private String service = "complex_upload";

    @ApiModelProperty(value = "平台分配唯一key", hidden = true)
    private String appKey;

    @ApiModelProperty(value = "接入平台id", hidden = true)
    private String accesserId;

    @ApiModelProperty(value = "签名方式", hidden = true)
    private String signType = "SHA-256";

    @ApiModelProperty(value = "请求流水号", required = true)
    @NotEmpty(message = "请求流水号不能为空")
    @Length(max = 30, message = "请求流水号长度不能超过30")
    private String requestSeq;

    @ApiModelProperty(value = "请求时间", hidden = true)
    private String requestDate = DateUtil.format(new Date(), "yyyyMMddhhmmss");

    @ApiModelProperty(value = "平台用户ID", hidden = true)
    private String accesserUserId;

    @ApiModelProperty(value = "注册类型(01：个人工商户;02：小微商户)", required = true)
    @NotEmpty(message = "注册类型不能为空")
    private String regMerType;

    @ApiModelProperty(value = "身份证姓名", required = true)
    @NotBlank(message = "身份证姓名不能为空")
    @Length(max = 30, message = "身份证姓名长度不能超过30")
    private String legalName;

    @ApiModelProperty(value = "身份证号", required = true)
    @NotBlank(message = "身份证号不能为空")
    @Length(max = 36, message = "身份证号长度不能超过36")
    private String legalIdcardNo;

    @ApiModelProperty(value = "base64格式的人脸图片(图片小于30K)", required = true)
    @NotBlank(message = "base64格式的人脸图片")
    private String faceImgBase64;

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "手机号不能为空")
    @Length(max = 30, message = "手机号长度不能超过30")
    private String legalMobile;

    @ApiModelProperty(value = "邮箱(开通泛账户必传)")
    @Length(max = 30, message = "邮箱长度不能超过30")
    private String legalEmail = "";

    @ApiModelProperty(value = "证件开始日期(yyyy-MM-dd)", required = true)
    @NotBlank(message = "证件开始日期不能为空")
    @Length(max = 10, message = "法人证件开始日期长度不能超过10")
    private String legalCardBeginDate;

    @ApiModelProperty(value = "证件截止日期(yyyy-MM-dd)", required = true)
    @NotBlank(message = "证件截止日期不能为空")
    @Length(max = 10, message = "证件截止日期长度不能超过10")
    private String legalCardDeadline;

    @ApiModelProperty(value = "家庭地址", required = true)
    @NotBlank(message = "家庭地址不能为空")
    @Length(max = 60, message = "家庭地址长度不能超过60")
    private String legalmanHomeAddr;

    @ApiModelProperty(value = "性别(企业类型为小微商户时必填 0:未知;1:男性;2:女性;)")
    @Length(max = 1, message = "性别长度不能超过1")
    private String legalSex;

    @ApiModelProperty(value = "职业(企业类型为小微商户时必填 0:各类专业、技术人员;1:国家机关、党群组织、企事业单位的负责人;2:办事人员和有关人员;3:商业工作人员;4:服务性工作人员;5:农林牧渔劳动者;6:生产工作、运输工作和部分体力劳动者;7:不便分类的其他劳动者;)")
    @Length(max = 1, message = "职业长度不能超过1")
    private String legalOccupation;

    @ApiModelProperty(value = "是否有营业场所(是否有营业场所，0代表没有，1代表有 / 小微商户必传字段且必须是0或者1 （传0则0005门头照片及0015店内照片选传；传1则0005门头照片及0015店内照片必传）)")
    private Integer havingFixedBusiAddr;

    @ApiModelProperty(value = "账户类型(0:个人账户;1:公司账户;)", required = true)
    @NotBlank(message = "账户类型不能为空")
    @Length(max = 1, message = "账户类型长度不能超过1")
    private String bankAcctType;

    @ApiModelProperty(value = "商户营业名称", required = true)
    @NotBlank(message = "商户营业名称不能为空")
    @Length(max = 60, message = "商户营业名称长度不能超过60")
    private String shopName;

    @ApiModelProperty(value = "开户行行号(所属支行查询接口返回)", required = true)
    @NotBlank(message = "开户行行号不能为空")
    @Length(max = 18, message = "开户行行号长度不能超过18")
    private String bankNo;

    @ApiModelProperty(value = "开户行帐号", required = true)
    @NotBlank(message = "开户行帐号不能为空")
    @Length(max = 40, message = "开户行帐号长度不能超过40")
    private String bankAcctNo;

    @ApiModelProperty(value = "开户帐号名称(对公账户填写公司名称，需与营业执照名称保持一致;个人账户填写姓名)", required = true)
    @NotBlank(message = "开户帐号名称不能为空")
    @Length(max = 127, message = "开户帐号名称长度不能超过127")
    private String bankAcctName;

    @ApiModelProperty(value = "营业省份id(接口查询返回)", required = true)
    @NotBlank(message = "营业省份id不能为空")
    @Length(max = 2, message = "营业省份id长度不能超过2")
    private String shopProvinceId;

    @ApiModelProperty(value = "营业市id(接口查询返回)", required = true)
    @NotBlank(message = "营业市id不能为空")
    @Length(max = 6, message = "营业市id长度不能超过6")
    private String shopCityId;

    @ApiModelProperty(value = "营业区id(接口查询返回)", required = true)
    @NotBlank(message = "营业区id不能为空")
    @Length(max = 6, message = "营业区id长度不能超过6")
    private String shopCountryId;

    @ApiModelProperty(value = "营业地址补充信息")
    @Length(max = 80, message = "营业地址补充信息长度不能超过80")
    private String shopAddrExt;

    @ApiModelProperty(value = "社会信用统一代码/营业执照(01：个人工商户（必填）;02：小微商户（选填）)")
    @Length(max = 50, message = "社会信用统一代码/营业执照长度不能超过50")
    private String shopLic;

    @ApiModelProperty(value = "行业类别编码(自助签约提供字典)", required = true)
    @NotNull(message = "行业类别编码不能为空")
    private Integer mccCode;

    @ApiModelProperty(value = "开通业务列表(暂时写死)", hidden = true)
    private List<JSONObject> product = new ArrayList<JSONObject>() {{
        String[] ids = {"8",/*"in1",*/"in2",/*"in3",*/"in4"};
        for (String id : ids) {
            // 开通业务id 字典项，暂时写死
            JSONObject productObject = new JSONObject();
            productObject.put("product_id", id);
            add(productObject);
        }
    }};

    @ApiModelProperty(value = "控股股东姓名(商户类型为非小微，且控股股东非法人时必填)")
    @Length(max = 20, message = "控股股东姓名长度不能超过20")
    private String shareholderName;

    @ApiModelProperty(value = "控股股东证件号(商户类型为非小微，且控股股东非法人时必填)")
    @Length(max = 20, message = "控股股东证件号长度不能超过20")
    private String shareholderCertno;

    @ApiModelProperty(value = "控股股东证件有效期(商户类型为非小微，且控股股东非法人时必填 yyyy-MM-dd)")
    @Length(max = 10, message = "控股股东证件有效期长度不能超过10")
    private String shareholderCertExpire;

    @ApiModelProperty(value = "控股股东证件开始日期(商户类型为非小微，且控股股东非法人时必填 yyyy-MM-dd)")
    @Length(max = 10, message = "控股股东证件有效期长度不能超过10")
    private String shareholderCertBeginDate;

    @ApiModelProperty(value = "控股股东证件证件类型(不填默认为身份证(1):1、身份证2、护照3、军官证4、警官证5、士兵证6、台湾居民来往大陆通行证7、回乡证8、港澳居民来往内地通行证10、港澳台居民居住证11、营业执照12、组织机构代码证13、税务登记证14、商业登记证15、民办非企业登记证书16、批文证明)")
    @Length(max = 3, message = "控股股东证件证件类型长度不能超过3")
    private String shareholderCertType;

    @ApiModelProperty(value = "控股股东家庭地址(商户类型为非小微，且控股股东非法人时必填)")
    @Length(max = 128, message = "控股股东家庭地址长度不能超过128")
    private String shareholderHomeAddr;

    @ApiModelProperty(value = "受益人列表(商户类型为非小微，且受益人非法人时必填)")
    private List<ChinaumsMerchantBnfDto> bnfList;

    @ApiModelProperty(value = "上传图片列表(必填)")
    private List<ChinaumsMerchantPicDto> picList;

    @ApiModelProperty(value = "是否连锁商户(0是，1否)")
    @NotNull(message = "是否连锁商户不能为空")
    private String isChain;

    @ApiModelProperty(value = "分支机构号(暂时写死)", hidden = true)
    private String instCd = "1022";
}
