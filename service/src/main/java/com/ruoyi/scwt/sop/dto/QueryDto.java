package com.ruoyi.scwt.sop.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 合利宝订单查询请求参数
 */
@Data
@ApiModel(value = "合利宝订单查询请求参数")
public class QueryDto {

    @ApiModelProperty(value = "订单号", required = true)
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "支付渠道")
    private String PayType;

    @ApiModelProperty(value = "订单时间(格式：yyyy-MM-dd)")
    private String billDate;
}
