package com.ruoyi.scwt.file.util;

import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.*;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.ruoyi.system.service.ISysConfigService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import com.aliyun.oss.common.auth.CredentialsProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;

import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;

import java.net.URL;
import java.util.Date;
import java.util.Map;

@Component
@Data
@Slf4j
public class OssUtil {
    @Value("${tencent.oss.secretId}")
    private String secretId;

    @Value("${tencent.oss.secretKey}")
    private String secretKey;

    @Value("${tencent.oss.region}")
    private String region;

    @Value("${tencent.oss.bucketName}")
    private String bucketName;

    @Value("${tencent.oss.downUrl}")
    private String downUrl;

    @Value("${tencent.oss.endpoint}")
    private String endpoint ;

    @Autowired
    private ISysConfigService configService;

    public String getBucketName() {
        return bucketName;
    }

    public OSS createCosClient() {
        // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(secretId, secretKey);

        // 创建OSSClient实例。
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        OSS ossClient = OSSClientBuilder.create()
                .endpoint(endpoint)
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(region)
                .build();
        return  ossClient;
    }


    public PutObjectResult fileUpload(String key, InputStream inputStream) throws IOException {
        OSS ossClient = createCosClient();

        try {
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, key, inputStream);

            // 创建PutObject请求。
            PutObjectResult result = ossClient.putObject(putObjectRequest);
            inputStream.close();
            return result;
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
            throw new RuntimeException("上传异常");
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
            throw new RuntimeException("oss客服端异常");
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    public String generatePreSignedUrl(String key, Map<String, String> params, Map<String, String> headers) {
        OSS ossClient = createCosClient();
        URL signedUrl = null;
        try {
            // 指定生成的签名URL过期时间，单位为毫秒。本示例以设置过期时间为1小时为例。
            Date expiration = new Date(new Date().getTime() + 1800 * 1000L);

            // 生成签名URL。
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, key, HttpMethod.PUT);
            // 设置过期时间。
            request.setExpiration(expiration);

            // 将请求头加入到request中。
            request.setHeaders(headers);
            // 添加用户自定义元数据。
            request.setUserMetadata(params);

            // 通过HTTP PUT请求生成签名URL。
            signedUrl = ossClient.generatePresignedUrl(request);
            // 打印签名URL。
//            System.out.println("signed url for putObject: " + signedUrl);
            return signedUrl.toString();
        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
            throw new RuntimeException("上传异常");
        } catch (ClientException ce) {
            System.out.println("Caught an ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
            throw new RuntimeException("oss客服端异常");
        }

    }

    public String getFileUrl(String key) {
        return downUrl+ StrUtil.SLASH + key;
    }

    /***
     * 检查文件是否是允许上传的格式
     */
    public boolean fileTypeVerify(String fileType){
        String fileTypes = configService.selectConfigByKey("FILE_TYPE_FILTER");
        if (fileTypes == null) {
            return true;
        } else {
            String[] types = fileTypes.split(",");
            return Arrays.asList(types).contains(fileType.toLowerCase());
        }
    }

    public String allowFileType() {
        return configService.selectConfigByKey("FILE_TYPE_FILTER");
    }

}
