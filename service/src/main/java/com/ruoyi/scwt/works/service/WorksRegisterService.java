package com.ruoyi.scwt.works.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.works.entity.WorksEvidence;
import com.ruoyi.scwt.works.entity.WorksRegister;
import com.ruoyi.scwt.works.vo.WorksRegisterVo;

/**
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
public interface WorksRegisterService extends IService<WorksRegister> {
    Long getRepeatName(String worksName, String userId);

    WorksRegisterVo setVoData(WorksRegister worksRegister, String voDataType);

    WorksRegister saveWorksRegister(WorksRegister worksRegister);

    WorksRegister isUpdate(Long worksId, Long userId);

    void addWorksRegister(WorksRegister worksRegister);

    void getWorkProgressByAcceptNo(WorksRegister worksRegister);

    R getCertificateById(Long worksId);

    R getRegisterCertificateById(Long worksId);
}
