package com.ruoyi.scwt.works.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 作品与著作权人映射表
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Data
@TableName("WORKS_OWNER_REL")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "作品与著作权人映射表")
public class WorksOwnerRel extends Model<WorksOwnerRel> {
private static final long serialVersionUID = 1L;
    /**
     * ID
     */
    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="ID")
    private Long id;
    /**
     * 类型
     */
    @ApiModelProperty(value="类型")
    private String type;
    /**
     * 作品ID
     */
    @ApiModelProperty(value="作品ID")
    private Long worksId;
    /**
     * 著作权人ID
     */
    @ApiModelProperty(value="著作权人ID")
    private Long ownerId;

    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createStaff;
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;

    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;
    }
