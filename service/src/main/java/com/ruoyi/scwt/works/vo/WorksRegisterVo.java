package com.ruoyi.scwt.works.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.works.entity.RegisterOwner;
import com.ruoyi.scwt.works.entity.WorksAuthor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 作品指丛书类每本，视频集每集，漫画丛书每本、影集每本、图片、单本小说等，其著作权、出版权都可能有所不同
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Data
public class WorksRegisterVo {

    /**
     * 作品ID，主键
     */
    @ApiModelProperty(value="作品ID",required = true)
    private Long worksId;
    /**
     * 受理号
     */
    @ApiModelProperty(value="受理号",hidden = true)
    @Excel(name = "受理号", prompt = "受理号")
    private String acceptanceNumber;
    /**
     * 作品名称
     */
    @ApiModelProperty(value="作品名称",required = true)
    @NotBlank(message = "作品名称不能为空")
    @Excel(name = "作品名称", prompt = "作品名称")
    private String worksName;
    /**
     * 作品类型
     */
    @ApiModelProperty(value="作品类型")
    @Excel(name = "作品类型", dictType = "worksInstWorksType")
    private String worksType;
    /**
     * 作品样本文件ID
     */

    @ApiModelProperty(value="作品样本文件")
    private AttachmentInfo worksSampleFile;
    /**
     * 权利保证书文件ID
     */
    @ApiModelProperty(value="权利保证书文件")
    private AttachmentInfo powerGuarantee;
    /**
     * 其他证明材料文件ID
     */
    @ApiModelProperty(value="其他证明材料文件")
    private List<AttachmentInfo> otherMaterials;
    /**
     * 作品简介
     */
    @ApiModelProperty(value="作品简介")
    @NotBlank(message = "作品简介不能为空")
    @Excel(name = "作品简介", prompt = "作品简介")
    private String worksAbstract;
    /**
     * 创作性质
     */
    @ApiModelProperty(value="创作性质",required = true)
    @NotBlank(message = "创作性质不能为空")
    @Excel(name = "创作性质", dictType = "worksInstCreateNature")
    private String inditeNature;
    /**
     * 作品归属情况
     */
    @ApiModelProperty(value="作品归属情况",required = true)
    @NotBlank(message = "作品归属情况不能为空")
    @Excel(name = "作品归属情况", dictType = "rightOwnershipWay")
    private String worksBelongType;
    /**
     * 权利取得方式
     */
    @ApiModelProperty(value="权利取得方式",required = true)
    @NotBlank(message = "权利取得方式不能为空")
    @Excel(name = "权利取得方式", dictType = "rightGet")
    private String copyrightObtainChannel;
    /**
     * 权利拥有方式
     */
    @ApiModelProperty(value="权利拥有方式,多个以逗号隔开",required = true)
    @NotBlank(message = "权利取得方式不能为空")
    private String copyrightOwnRange;
    /**
     * 作品HASH
     */
    @ApiModelProperty(value="作品HASH",hidden = true)
    private String worksHash;
    /**
     * 上链时间
     */
    @ApiModelProperty(value="上链时间",hidden = true)
    private String blockchainTime;
    /**
     * 上链HASH
     */
    @ApiModelProperty(value="上链HASH",hidden = true)
    private String blockchainHash;
    /**
     * 存证证书地址
     */
    @ApiModelProperty(value="存证证书地址",hidden = true)
    private String certificateUrl;
    /**
     * 省版权局证书编号
     */
    @ApiModelProperty(value="省版权局证书编号",hidden = true)
    @Excel(name = "省版权局证书编号", prompt = "省版权局证书编号")
    private String registerCertificateNo;
    /**
     * 省版权局证书地址
     */
    @ApiModelProperty(value="省版权局证书地址",hidden = true)
    private String registerCertificateUrl;
    /**
     * 省版权局证书获取时间
     */
    @ApiModelProperty(value="省版权局证书获取时间",hidden = true)
    private String registerCertificateTime;
    /**
     * 创作完成时间
     */
    @ApiModelProperty(value="创作完成时间",required = true)
    @NotNull(message = "创作完成时间不能为空")
    private LocalDateTime inditeDoneTime;
    /**
     * 创作完成地点
     */
    @ApiModelProperty(value="创作完成地点",required = true)
    @NotNull(message = "创作完成地点不能为空")
    private String inditeDonePlace;

    /**
     * 首次发表日期
     */
    @ApiModelProperty(value="首次发表日期")
    private LocalDateTime publishDate;

    /**
     * 发表地点
     */
    @ApiModelProperty(value="发表地点")
    private String publishPlace;
    /**
     * 审核意见
     */
    @ApiModelProperty(value="审核意见")
    private String auditOpinion;
    @ApiModelProperty(value="作品署名")
    private String worksSignature;
    @ApiModelProperty(value="分发权利")
    private String distributeRight;
    @ApiModelProperty(value="代理登记")
    private String agentRegister;
    @ApiModelProperty(value="状态",hidden = true)
    @Excel(name = "状态", readConverterExp = "1000=登记完成,1300=审核中,1101=上传作品,1102=填写基本信息,1103=提交证明材料,1200=存证中,1301=审核驳回,1900=删除")
    private String statusCd;
    @ApiModelProperty(value="创建时间",hidden = true)
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;


    @ApiModelProperty(value="著作权人列表")
    private List<RegisterOwner> ownerList;

    @ApiModelProperty(value="作者列表")
    private List<WorksAuthor> authorList;

    @ApiModelProperty(value="著作权人名称")
    @TableField(exist = false)
    @Excel(name = "著作权人名称", prompt = "著作权人名称")
    private String ownerNames;

    }
