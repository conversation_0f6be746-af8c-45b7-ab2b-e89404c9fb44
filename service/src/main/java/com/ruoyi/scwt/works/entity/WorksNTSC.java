package com.ruoyi.scwt.works.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020/7/30 9:15
 */
@Data
@TableName("WORKS_NTSC")
@EqualsAndHashCode()
@ApiModel(value = "时间戳")
public class WorksNTSC {


    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="ID，主键")
    private Long id;
    /**
     * 作品id
     */
    @ApiModelProperty(value="作品id")
    private Long worksId;
    /**
     * 时间戳返回时间
     */
    @ApiModelProperty(value="时间戳返回时间")
    private String ntscTime;
    /**
     * 时间戳返回信息
     */
    @ApiModelProperty(value="时间戳返回信息")
    private byte[] res;

    /**
     * 业务类型
     */
    @ApiModelProperty(value="业务类型数据")
    private String type;

}
