package com.ruoyi.scwt.works.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.scwt.common.constant.CommonConstants;
import com.ruoyi.scwt.common.controller.AliIdentifyController;
import com.ruoyi.scwt.common.dto.AliIdentifyDto;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.shop.vo.ShopInfoVo;
import com.ruoyi.scwt.sop.DataProcessing.Bmark2Sop;
import com.ruoyi.scwt.works.constant.OwnerConstants;
import com.ruoyi.scwt.works.constant.WorksConstants;
import com.ruoyi.scwt.works.dto.EvidenceOwnerDto;
import com.ruoyi.scwt.works.dto.WorksEvidenceDto;
import com.ruoyi.scwt.works.entity.*;
import com.ruoyi.scwt.works.mapper.WorksNTSCMapper;
import com.ruoyi.scwt.works.service.EvidenceOwnerService;
import com.ruoyi.scwt.works.service.RegisterOwnerService;
import com.ruoyi.scwt.works.service.WorksEvidenceService;
import com.ruoyi.scwt.works.service.WorksOwnerRelService;
import com.ruoyi.scwt.works.vo.WorksEvidenceVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@RestController
@AllArgsConstructor
@RequestMapping("/worksEvidence")
@Api(value = "worksEvidence", tags = "存证作品管理")
public class WorksEvidenceController extends BaseController {

    private final WorksNTSCMapper worksNTSCMapper;

    private final WorksEvidenceService worksEvidenceService;

    private final WorksOwnerRelService worksOwnerRelService;

    private final RegisterOwnerService registerOwnerService;

    private final AttachmentInfoService attachmentInfoService;

    /**
     * 分页查询
     *
     * @param dto 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public R getFileEvidenceOwnerPage(WorksEvidenceDto dto) {
        List<Long> worksIds=new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getOwnerName())){
            List<Long> ownerIds = registerOwnerService.list(Wrappers.<RegisterOwner>query().lambda().like(RegisterOwner::getOwnerName, dto.getOwnerName()).eq(RegisterOwner::getStatusCd, OwnerConstants.OWNER_TAKE_EFFECT)).stream().map(RegisterOwner::getOwnerId).collect(Collectors.toList());
            worksIds = worksOwnerRelService.list(Wrappers.<WorksOwnerRel>query().lambda().in(WorksOwnerRel::getOwnerId, ownerIds)).stream().map(WorksOwnerRel::getWorksId).collect(Collectors.toList());
        }
        LambdaQueryWrapper<WorksEvidence> lambda = Wrappers.<WorksEvidence>query().lambda()
                .like(StringUtils.isNotEmpty(dto.getWorksName()), WorksEvidence::getWorksName, dto.getWorksName())
                .ge(ObjectUtil.isNotEmpty(dto.getStartDate()), WorksEvidence::getCreateDate, dto.getStartDate())
                .eq(StringUtils.isNotEmpty(dto.getStatusCd()),WorksEvidence::getStatusCd, dto.getStatusCd())
                .ne(StringUtils.isEmpty(dto.getStatusCd()),WorksEvidence::getStatusCd, WorksConstants.WORKS_DELETE)
                .le(ObjectUtil.isNotEmpty(dto.getEndDate()), WorksEvidence::getCreateDate, dto.getEndDate())
                .eq(!SecurityUtils.isAdmin(getUserId()), WorksEvidence::getCreateStaff, getUserId())
                .in(worksIds.size()>0,WorksEvidence::getWorksId,worksIds)
                .orderByAsc(WorksEvidence::getCreateDate);
        startPage();
        List<WorksEvidence> list = worksEvidenceService.list(lambda);
        List<WorksEvidenceVo> voList =  list.stream().map(item -> worksEvidenceService.setVoData(item, CommonConstants.VO_DATA_PAGE)).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);
    }


    @ApiOperation(value = "excel导出", notes = "excel导出")
    @PostMapping("/page/export")
    public void getFileEvidenceOwnerPage(HttpServletResponse response, WorksEvidenceDto dto) {
        List<Long> worksIds=new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getOwnerName())){
            List<Long> ownerIds = registerOwnerService.list(Wrappers.<RegisterOwner>query().lambda().like(RegisterOwner::getOwnerName, dto.getOwnerName()).eq(RegisterOwner::getStatusCd, OwnerConstants.OWNER_TAKE_EFFECT)).stream().map(RegisterOwner::getOwnerId).collect(Collectors.toList());
            worksIds = worksOwnerRelService.list(Wrappers.<WorksOwnerRel>query().lambda().in(WorksOwnerRel::getOwnerId, ownerIds)).stream().map(WorksOwnerRel::getWorksId).collect(Collectors.toList());
        }
        LambdaQueryWrapper<WorksEvidence> lambda = Wrappers.<WorksEvidence>query().lambda()
                .like(StringUtils.isNotEmpty(dto.getWorksName()), WorksEvidence::getWorksName, dto.getWorksName())
                .ge(ObjectUtil.isNotEmpty(dto.getStartDate()), WorksEvidence::getCreateDate, dto.getStartDate())
                .eq(StringUtils.isNotEmpty(dto.getStatusCd()),WorksEvidence::getStatusCd, dto.getStatusCd())
                .ne(StringUtils.isEmpty(dto.getStatusCd()),WorksEvidence::getStatusCd, WorksConstants.WORKS_DELETE)
                .le(ObjectUtil.isNotEmpty(dto.getEndDate()), WorksEvidence::getCreateDate, dto.getEndDate())
                .eq(!SecurityUtils.isAdmin(getUserId()), WorksEvidence::getCreateStaff, getUserId())
                .in(worksIds.size()>0,WorksEvidence::getWorksId,worksIds)
                .orderByAsc(WorksEvidence::getCreateDate);
//        startPage();
        List<WorksEvidence> list = worksEvidenceService.list(lambda);
        List<WorksEvidenceVo> voList =  list.stream().map(item -> worksEvidenceService.setVoData(item, CommonConstants.VO_DATA_PAGE)).collect(Collectors.toList());
        ExcelUtil<WorksEvidenceVo> util = new ExcelUtil<WorksEvidenceVo>(WorksEvidenceVo.class);
        util.exportExcel(response, voList, "订单数据");
    }




    /**
     * 通过id查询
     *
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping
    public R getById(Long id) {
        WorksEvidence worksEvidence = worksEvidenceService.getById(id);
        WorksEvidenceVo worksEvidenceVo = worksEvidenceService.setVoData(worksEvidence, CommonConstants.VO_DATA_DETAIL);
        return R.ok(worksEvidenceVo);
    }

    /**
     * 新增
     *
     * @param worksEvidence
     * @return R
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping
    public R save(@Validated @RequestBody WorksEvidence worksEvidence) {
        String userId = String.valueOf(SecurityUtils.getUserId());
        Long count=worksEvidenceService.getRepeatName(worksEvidence.getWorksName(),userId);
        if (count>0) {
            return R.fail("作品名称重复");
        }
//        worksEvidence.setWorksHash(attachmentInfoService.getById(worksEvidence.getWorksFileId()).getHashCode());
        worksEvidence.setAcceptanceNumber(worksEvidenceService.createAcceptanceNumber(userId));
        worksEvidence.setCreateStaff(userId);
        worksEvidence.setStatusCd(WorksConstants.WORKS_REGISTER_PAY);
        worksEvidence.setCreateDate(LocalDateTime.now());
        worksEvidence.setStatusDate(LocalDateTime.now());
        worksEvidenceService.save(worksEvidence);
        worksOwnerRelService.insertOwnerRel(worksEvidence.getWorksId(),worksEvidence.getOwnerIds(),WorksConstants.WORKS_TYPE_EVIDENCE);
//        worksEvidenceService.addWorksEvidence(worksEvidence);
        return R.ok(worksEvidence,"存证作品新增成功");
    }

    /**
     * 修改
     *
     * @param worksEvidence
     * @return R
     */
    @ApiOperation(value = "修改", notes = "修改")
    @PutMapping
    public R updateById(@Validated @RequestBody WorksEvidence worksEvidence) {
        String userId = String.valueOf(SecurityUtils.getUserId());
        Long count=worksEvidenceService.getRepeatName(worksEvidence.getWorksName(),userId);
        if (count>0) {
            return R.fail("作品名称重复");
        }
        worksEvidence.setUpdateStaff(String.valueOf(SecurityUtils.getUserId()));
        worksEvidence.setUpdateDate(LocalDateTime.now());
        worksEvidence.setStatusCd(WorksConstants.WORKS_EVIDENCE);
        worksOwnerRelService.remove(Wrappers.<WorksOwnerRel>query().lambda().eq(WorksOwnerRel::getOwnerId, worksEvidence.getWorksId()).eq(WorksOwnerRel::getType, WorksConstants.WORKS_TYPE_EVIDENCE));
        worksOwnerRelService.insertOwnerRel(worksEvidence.getWorksId(),worksEvidence.getOwnerIds(),WorksConstants.WORKS_TYPE_EVIDENCE);
        worksEvidenceService.updateById(worksEvidence);
        return R.ok("修改完成");
    }


    /**
     * 修改
     *
     * @return R
     */
    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping
    public R updateStatusCd(Long worksId) {
        Long userId = SecurityUtils.getUserId();
        WorksEvidence worksEvidence=worksEvidenceService.isUpdate(worksId,userId);
        if (ObjectUtil.isEmpty(worksEvidence)) {
            return R.fail("无权限删除");
        }
        worksEvidence.setUpdateStaff(String.valueOf(userId));
        worksEvidence.setUpdateDate(LocalDateTime.now());
        worksEvidence.setStatusCd(WorksConstants.WORKS_DELETE);
        worksEvidence.setStatusDate(LocalDateTime.now());
        worksEvidenceService.updateById(worksEvidence);
        return R.ok("删除成功");
    }

    @ApiOperation(value = "获取存证证书地址", notes = "获取存证证书地址")
    @GetMapping("/getCertificateById")
    public R getCertificateById(Long worksId) {
        String certificate = worksEvidenceService.getCertificateById(worksId);
        return R.ok(attachmentInfoService.getObjectUrl(certificate));
    }
    @GetMapping("/downloadCert")
    @ApiOperation(value = "下载时间戳证书", notes = "下载时间戳证书")
    public void downloadCert(Long worksId, HttpServletResponse response) {
        OutputStream os = null;
        try {
            response.setHeader(HttpHeaders.CONTENT_TYPE,"application/json;charset=UTF-8");
            os = response.getOutputStream();
            os.write(worksNTSCMapper.selectOne(Wrappers.<WorksNTSC>lambdaQuery().eq(WorksNTSC::getWorksId, worksId).eq(WorksNTSC::getType, WorksConstants.WORKS_TYPE_EVIDENCE)).getRes());
            os.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            try {
                assert os != null;
                os.close();
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
        }
    }
}
