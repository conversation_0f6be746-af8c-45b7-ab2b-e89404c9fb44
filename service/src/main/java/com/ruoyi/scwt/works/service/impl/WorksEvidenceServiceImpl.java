package com.ruoyi.scwt.works.service.impl;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.scwt.common.constant.CommonConstants;
import com.ruoyi.scwt.common.util.AESUtils;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.file.util.OssUtil;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.mapper.IdentifyMapper;
import com.ruoyi.scwt.sop.DataProcessing.Bmark2Sop;
import com.ruoyi.scwt.sop.controller.EvidenceAndRegisterController;
import com.ruoyi.scwt.sop.dto.FileEvidenceDto;
import com.ruoyi.scwt.works.constant.WorksConstants;
import com.ruoyi.scwt.works.dto.EvidenceCertificateDto;
import com.ruoyi.scwt.works.entity.*;
import com.ruoyi.scwt.works.mapper.WorksEvidenceMapper;
import com.ruoyi.scwt.works.mapper.WorksNTSCMapper;
import com.ruoyi.scwt.works.service.EvidenceOwnerService;
import com.ruoyi.scwt.works.service.WorksEvidenceService;
import com.ruoyi.scwt.works.service.WorksOwnerRelService;
import com.ruoyi.scwt.works.util.PdfUtil;
import com.ruoyi.scwt.works.vo.WorksEvidenceVo;
import com.ruoyi.system.mapper.SysUserMapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
//@AllArgsConstructor
@Slf4j
public class WorksEvidenceServiceImpl extends ServiceImpl<WorksEvidenceMapper, WorksEvidence> implements WorksEvidenceService {

    @Autowired
    private EvidenceOwnerService evidenceOwnerService;

    @Autowired
    private AttachmentInfoService attachmentInfoService;

    @Autowired
    private WorksOwnerRelService worksOwnerRelService;

    @Autowired
    private Bmark2Sop bmark2Sop;

    @Autowired
    private EvidenceAndRegisterController evidenceAndRegisterController;

    @Value("${font.file}")
    private String fontFile;
    @Resource
    private WorksNTSCMapper worksNTSCMapper;
    @Resource
    private IdentifyMapper identifyMapper;

    @Resource
    private SysUserMapper sysUserMapper;

    @Override
    public Long getRepeatName(String worksName, String userId) {
        long count = baseMapper.selectCount(Wrappers.<WorksEvidence>lambdaQuery()
                .eq(WorksEvidence::getWorksName, worksName)
                .eq(WorksEvidence::getCreateStaff, userId)
                .and(i -> i.eq(WorksEvidence::getStatusCd, WorksConstants.WORKS_DONE).or()
                        .eq(WorksEvidence::getStatusCd, WorksConstants.WORKS_REVIEW).or()
                        .eq(WorksEvidence::getStatusCd, WorksConstants.WORKS_EVIDENCE)));
        return count;
    }

    @Override
    public WorksEvidenceVo setVoData(WorksEvidence worksEvidence, String voDataType) {
        WorksEvidenceVo worksEvidenceVo = new WorksEvidenceVo();
        BeanUtils.copyProperties(worksEvidence, worksEvidenceVo);
        List<WorksOwnerRel> list = worksOwnerRelService.list(Wrappers.<WorksOwnerRel>lambdaQuery().eq(WorksOwnerRel::getWorksId, worksEvidence.getWorksId()).eq(WorksOwnerRel::getType, WorksConstants.WORKS_TYPE_EVIDENCE));
        List<EvidenceOwner> ownerList=new ArrayList<>();
        for (WorksOwnerRel s : list) {
            EvidenceOwner byId = evidenceOwnerService.getById(s.getOwnerId());
            ownerList.add(byId);
        }
        worksEvidenceVo.setOwnerNames(ownerList.stream().map(EvidenceOwner::getOwnerName).collect(Collectors.joining(",")));
        worksEvidenceVo.setOwnerList(ownerList);
        if (voDataType.equals(CommonConstants.VO_DATA_DETAIL)) {
            worksEvidenceVo.setWorksSampleFile(attachmentInfoService.getById(worksEvidence.getWorksFileId()));
            if (ObjectUtil.isNotEmpty(worksEvidence.getTsaId())){
                WorksNTSC worksNTSC = worksNTSCMapper.selectOne(Wrappers.<WorksNTSC>lambdaQuery().eq(WorksNTSC::getWorksId, worksEvidence.getWorksId()));
                String date = DateUtil.date(Long.parseLong(worksNTSC.getNtscTime())).toString();
                worksEvidenceVo.setTsaTime(date + "（UTC/GMT+08:00）");
            }
        }
        return worksEvidenceVo;
    }


    @Override
    public WorksEvidence isUpdate(Long worksId,Long userId) {
        WorksEvidence worksEvidence = baseMapper.selectOne(Wrappers.<WorksEvidence>lambdaQuery()
                .eq(WorksEvidence::getWorksId, worksId)
                .eq(!SecurityUtils.isAdmin(userId), WorksEvidence::getCreateStaff, userId)
                .and(i -> i.ne(WorksEvidence::getStatusCd, WorksConstants.WORKS_DONE).or()
                        .ne(WorksEvidence::getStatusCd, WorksConstants.WORKS_EVIDENCE)));
        return worksEvidence;
    }

    @Override
    @Async
    public void addWorksEvidence(WorksEvidence worksEvidence) {
        FileEvidenceDto fileEvidenceDto=new FileEvidenceDto();
        //作品名称
        fileEvidenceDto.setWorkName(worksEvidence.getWorksName());
        //样本文件ID
        List<Long> sampleFileId = new ArrayList<>();
        String ybwj = bmark2Sop.fileUploadDataJoin(String.valueOf(worksEvidence.getWorksFileId()), com.ruoyi.scwt.sop.constant.CommonConstants.FILE_TYPE_YBWJ, worksEvidence.getCreateStaff(), "样本文件");
        sampleFileId.add(Long.valueOf(ybwj));
        fileEvidenceDto.setAttachIds(sampleFileId);
        //著作权人ID
        List<EvidenceOwner> ownersList = worksOwnerRelService.getEvidenceOwnerList(worksEvidence.getWorksId());
        for (EvidenceOwner owner : ownersList) {
            if (StrUtil.isEmpty(owner.getSopOwnerId())) {
                evidenceOwnerService.addSopOwner(owner);
                EvidenceOwner evidenceOwner = evidenceOwnerService.getById(owner.getOwnerId());
                owner.setSopOwnerId(evidenceOwner.getSopOwnerId());
            }
        }
        fileEvidenceDto.setOwnerIds(ownersList.stream().map(EvidenceOwner::getSopOwnerId).map(Long::valueOf).collect(Collectors.toList()));
        fileEvidenceDto.setPlatformUserId(worksEvidence.getCreateStaff());
        JSONObject jsonObject = evidenceAndRegisterController.worksEvidenceSave(fileEvidenceDto);
//        System.out.println(jsonObject.toString());
        if (jsonObject.getStr("statusCd").equals(com.ruoyi.scwt.sop.constant.CommonConstants.EVIDENCE_STATUS_SUCCESS)){
            worksEvidence.setSopAcceptanceNumber(jsonObject.getStr("acceptanceNumber"));
            worksEvidence.setSopWorksId(jsonObject.getStr("worksId"));
            worksEvidence.setWorksHash(jsonObject.getStr("worksHash"));
            worksEvidence.setTsaId(jsonObject.getLong("tsaId"));
            worksEvidence.setBlockchainId(jsonObject.getStr("dacId"));
            worksEvidence.setBlockchainTime(jsonObject.getStr("blockTime"));
            worksEvidence.setBlockchainHash(jsonObject.getStr("blockHash"));
            worksEvidence.setStatusCd(WorksConstants.WORKS_DONE);
            baseMapper.updateById(worksEvidence);
            JSONObject worksNTSCjson = jsonObject.getJSONObject("worksNTSC");
            WorksNTSC worksNTSC = new WorksNTSC();
            worksNTSC.setWorksId(worksEvidence.getWorksId());
            worksNTSC.setNtscTime(worksNTSCjson.getStr("ntscTime"));
            worksNTSC.setRes(worksNTSCjson.getStr("res").getBytes());
            worksNTSC.setType(WorksConstants.WORKS_TYPE_EVIDENCE);
            worksNTSCMapper.insert(worksNTSC);
        }
    }

    @Override
    public String createAcceptanceNumber(String userId) {
        String acceptanceNumber = "CWSF";
        String time = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String traceId = UUID.randomUUID().toString().substring(0, 5).toUpperCase();
        return acceptanceNumber+userId+time+traceId;
    }

    @Override
    public String getCertificateById(Long worksId) {
        WorksEvidence worksEvidence = baseMapper.selectOne(Wrappers.<WorksEvidence>lambdaQuery().eq(WorksEvidence::getWorksId, worksId).eq(WorksEvidence::getStatusCd, WorksConstants.WORKS_DONE));
        if (worksEvidence == null){
            return null;
        }
        if (StringUtils.isNotEmpty(worksEvidence.getCertificateUrl())){
            return worksEvidence.getCertificateUrl();
        }else {
            return evidenceWorksCertificate(worksEvidence);
        }
    }

    public String evidenceWorksCertificate(WorksEvidence worksEvidence) {
        EvidenceCertificateDto dto = new EvidenceCertificateDto();
        dto.setAcceptanceNumber(worksEvidence.getAcceptanceNumber());
        dto.setWorksName(worksEvidence.getWorksName());
        dto.setWorksHash(worksEvidence.getWorksHash().substring(0,49));
        dto.setWorksHashLater(worksEvidence.getWorksHash().substring(49));
        dto.setFileNum("1");
        dto.setBlockchainHash(worksEvidence.getBlockchainHash().substring(0,49));
        dto.setBlockchainHashLater(worksEvidence.getBlockchainHash().substring(49));
        Identify identify = identifyMapper.selectOne(Wrappers.<Identify>lambdaQuery().eq(Identify::getUserId, worksEvidence.getCreateStaff()));
        dto.setIdName(identify.getIdName());
//        dto.setIdNo(AESUtils.decrypt(identify.getIdNo()));
        dto.setIdNo(identify.getIdNo());
        WorksNTSC worksNTSC = worksNTSCMapper.selectOne(Wrappers.<WorksNTSC>lambdaQuery().eq(WorksNTSC::getWorksId, worksEvidence.getWorksId()).eq(WorksNTSC::getType,WorksConstants.WORKS_TYPE_EVIDENCE));
        String date = DateUtil.date(Long.parseLong(worksNTSC.getNtscTime())).toString();
        dto.setTsaTime(date + "（UTC/GMT+08:00）");
        SysUser sysUser = sysUserMapper.selectUserById(Long.valueOf(worksEvidence.getCreateStaff()));
        dto.setEvidenceAccount(sysUser.getUserName());
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            PdfUtil.generate(out, "evidence_certificate.ftl", dto, fontFile);
            MultipartFile multipartFile = new MockMultipartFile(worksEvidence.getWorksName() + "-存证证书.pdf", worksEvidence.getWorksName() + "-存证证书.pdf",
                    "application/pdf", out.toByteArray());
            AttachmentInfo infoVo = attachmentInfoService.upload(multipartFile, "evidenceCertificate", worksEvidence.getWorksName() + "-存证证书.pdf", worksEvidence.getCreateStaff());
            worksEvidence.setCertificateUrl(infoVo.getFilePath());
            baseMapper.updateById(worksEvidence);
            return infoVo.getFilePath();
        } catch (Exception e) {
            log.error("下载证书文件失败:{}", e);
            return e.getMessage();
        }
    }

}
