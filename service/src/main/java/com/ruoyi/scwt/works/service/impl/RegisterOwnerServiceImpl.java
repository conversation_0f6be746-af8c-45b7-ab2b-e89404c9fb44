package com.ruoyi.scwt.works.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scwt.common.controller.AliIdentifyController;
import com.ruoyi.scwt.common.dto.AliIdentifyDto;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.sop.DataProcessing.Bmark2Sop;
import com.ruoyi.scwt.works.constant.OwnerConstants;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.entity.RegisterOwner;
import com.ruoyi.scwt.works.mapper.EvidenceOwnerMapper;
import com.ruoyi.scwt.works.mapper.RegisterOwnerMapper;
import com.ruoyi.scwt.works.service.EvidenceOwnerService;
import com.ruoyi.scwt.works.service.RegisterOwnerService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
@RequiredArgsConstructor
public class RegisterOwnerServiceImpl extends ServiceImpl<RegisterOwnerMapper, RegisterOwner> implements RegisterOwnerService {

    private final Bmark2Sop bmark2Sop;
    private final IdentifyService identifyService;
    private final AliIdentifyController aliIdentifyController;
    @Override
    @Async
    public RegisterOwner addSopOwner(RegisterOwner registerOwner) {
        JSONObject jsonObject = bmark2Sop.addOwner(registerOwner, "registration", registerOwner.getCreateStaff());
        registerOwner.setSopOwnerId(jsonObject.getStr("id"));
        baseMapper.updateById(registerOwner);
        return registerOwner;
    }

    @Override
    public R insert(RegisterOwner registerOwner) {
        if (!identifyService.isIdentify(registerOwner.getCreateStaff())){
            return R.fail("请先完成实名认证");
        }
        RegisterOwner one = baseMapper.selectOne(Wrappers.<RegisterOwner>lambdaQuery()
                .eq(RegisterOwner::getOwnerName, registerOwner.getOwnerName())
                .eq(RegisterOwner::getIdNo, registerOwner.getIdNo())
                .eq(RegisterOwner::getStatusCd, OwnerConstants.OWNER_TAKE_EFFECT)
                .eq(StringUtils.isNotEmpty(registerOwner.getLegalName()), RegisterOwner::getLegalName,registerOwner.getLegalName())
                .eq(RegisterOwner::getCreateStaff,registerOwner.getCreateStaff()));
        if(ObjectUtil.isNotNull(one)){
            return R.fail("该著作权人已存在");
        }
        AliIdentifyDto identify=new AliIdentifyDto(registerOwner.getOwnerType(),registerOwner.getOwnerName(),registerOwner.getIdNo(),registerOwner.getLegalName());
        R r = aliIdentifyController.aliIdentify(identify);
        if (!(Boolean) r.getData()) {
            return R.fail(r.getMsg());
        }
        registerOwner.setStatusCd(OwnerConstants.OWNER_TAKE_EFFECT);
        registerOwner.setCreateDate(LocalDateTime.now());
        registerOwner.setStatusDate(LocalDateTime.now());
        baseMapper.insert(registerOwner);
        addSopOwner(registerOwner);
        return R.ok("登记著作权人新增成功");
    }

}
