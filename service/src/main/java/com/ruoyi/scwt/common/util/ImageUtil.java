package com.ruoyi.scwt.common.util;


import cn.hutool.core.codec.Base64;
import java.awt.AlphaComposite;
import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.GraphicsConfiguration;
import java.awt.GraphicsDevice;
import java.awt.GraphicsEnvironment;
import java.awt.HeadlessException;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.awt.image.ImageObserver;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import javax.imageio.ImageIO;
import javax.swing.ImageIcon;
import org.springframework.util.Assert;
import net.coobird.thumbnailator.Thumbnails;

public class ImageUtil {
    public ImageUtil() {
    }

    public static BufferedImage image(String backgroundPath, int bgWidth, int bgHeight, BufferedImage wxImage, int smWidth, int smHeight, int x, int y) {
        try {
            Assert.hasText(backgroundPath, "底图路径为空");
            BufferedImage image = getBufferedImageFromUrl(backgroundPath, "png");
            BufferedImage bufferedImage = resizeImage(image, bgWidth, bgHeight);
            Graphics g = bufferedImage.getGraphics();
            g.drawImage(wxImage, x, y, smWidth, smHeight, (ImageObserver)null);
            return bufferedImage;
        } catch (IOException var11) {
            throw new RuntimeException("合成图片失败", var11);
        }
    }

    public static BufferedImage getBufferedImageFromUrl(String imgUrl, String mimeType) throws IOException {
        if (!imgUrl.startsWith("https://") && !imgUrl.startsWith("http://")) {
            return ImageIO.read(new File(imgUrl));
        } else if (!mimeType.toUpperCase(Locale.ROOT).equals("JPG") && !mimeType.toUpperCase(Locale.ROOT).equals("JPEG")) {
            return ImageIO.read(new URL(imgUrl));
        } else {
            URL url = new URL(imgUrl);
            ImageIcon icon = new ImageIcon(url);
            Image image = icon.getImage();
            BufferedImage bimage = null;
            GraphicsEnvironment ge = GraphicsEnvironment.getLocalGraphicsEnvironment();

            byte type;
            try {
                type = 1;
                GraphicsDevice gs = ge.getDefaultScreenDevice();
                GraphicsConfiguration gc = gs.getDefaultConfiguration();
                bimage = gc.createCompatibleImage(image.getWidth((ImageObserver)null), image.getHeight((ImageObserver)null), type);
            } catch (HeadlessException var10) {
            }

            if (bimage == null) {
                type = 1;
                bimage = new BufferedImage(image.getWidth((ImageObserver)null), image.getHeight((ImageObserver)null), type);
            }

            Graphics g = bimage.createGraphics();
            g.drawImage(image, 0, 0, (ImageObserver)null);
            g.dispose();
            return bimage;
        }
    }

    public static BufferedImage resizeImage(BufferedImage originalImage, int targetWidth, int targetHeight) {
        Image resultingImage = originalImage.getScaledInstance(targetWidth, targetHeight, 16);
        BufferedImage outputImage = new BufferedImage(targetWidth, targetHeight, 1);
        outputImage.getGraphics().drawImage(resultingImage, 0, 0, (ImageObserver)null);
        return outputImage;
    }

    public static String imgCompress(String url, float quality, float scale, String mimeType) throws IOException {
        BufferedImage image = getBufferedImageFromUrl(url, mimeType);
        return imgCompress(image, quality, scale, mimeType);
    }

    public static String imgCompress(BufferedImage image, float quality, float scale, String mimeType) throws IOException {
        BufferedImage thumbnail = Thumbnails.of(new BufferedImage[]{image}).scale((double)scale).outputQuality(quality).outputFormat(mimeType).asBufferedImage();
        return calBase64(thumbnail, mimeType);
    }

//    public static String calBase64(BufferedImage image, String mimeType) throws IOException {
//        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//        ImageIO.write(image, mimeType, outputStream);
//        BASE64Encoder encoder = new BASE64Encoder();
//        return "data:image/" + mimeType + ";base64," + encoder.encodeBuffer(outputStream.toByteArray());
//    }

        public static String calBase64(BufferedImage image, String mimeType) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(image, mimeType, outputStream);
        // 替换为 java.util.Base64
        return "data:image/" + mimeType + ";base64," +
                java.util.Base64.getEncoder().encodeToString(outputStream.toByteArray());
    }

    public static String word2Image(String url, float alpha, String font, int fontStyle, int fontSize, Color color, List<String> inputWords, int x, int y, String imageFormat) {
        try {
            BufferedImage bufferedImage = getBufferedImageFromUrl(url, imageFormat);
            int width = bufferedImage.getWidth();
            int height = bufferedImage.getHeight();
            Graphics2D graphics = bufferedImage.createGraphics();
            graphics.drawImage(bufferedImage, 0, 0, width, height, (Color)null, (ImageObserver)null);
            AlphaComposite alphaComposite = AlphaComposite.getInstance(3, alpha);
            graphics.setComposite(alphaComposite);
            graphics.setFont(new Font(font, fontStyle, fontSize));
            graphics.setColor(color);

            for(Iterator var15 = inputWords.iterator(); var15.hasNext(); y += 102) {
                String inputWord = (String)var15.next();
                x+=103;
                graphics.drawString(inputWord, x, y);
            }

            graphics.dispose();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, imageFormat, outputStream);
            return "data:image/" + imageFormat + ";base64," + Base64.encode(outputStream.toByteArray());
        } catch (Exception var17) {
            var17.printStackTrace();
            return null;
        }
    }

    public static void main(String[] args) throws IOException {
        String url = "D:\\download\\test\\hehe.jpg";
        String png = imgCompress(url, 0.2F, 1.0F, "jpg");
    }
}

