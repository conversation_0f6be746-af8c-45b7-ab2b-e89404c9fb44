package com.ruoyi.scwt.common.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scwt.common.entity.SysLabel;
import com.ruoyi.scwt.common.entity.SysRegion;
import com.ruoyi.scwt.common.service.SysLabelService;
import com.ruoyi.scwt.common.service.SysRegionService;
import com.ruoyi.scwt.common.vo.RegionVO;
import com.ruoyi.scwt.common.vo.SysLabelVo;
import com.ruoyi.scwt.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;


/**
 * 全国省市区
 *
 * <AUTHOR>
 * @date 2019-10-17 16:21:21
 */
@RestController
@AllArgsConstructor
@RequestMapping("/sysLabel")
@Api(value = "sysLabel",tags = "标签管理")
public class SysLabelController extends BaseController {

  private final SysLabelService sysLabelService;

  /**
   * 分页查询
   * @param sysLabelVo
   * @return
   */
  @GetMapping("/page")
  @ApiOperation(value="分页查询标签")
  @Anonymous
  public TableDataInfo getRegionPage(SysLabelVo sysLabelVo) {
    LambdaQueryWrapper<SysLabel> lambda = Wrappers.<SysLabel>query().lambda()
            .eq(SysLabel::getLabelType, sysLabelVo.getLabelType())
            .orderByAsc(SysLabel::getLabelSort);
    startPage();
    List<SysLabel> list = sysLabelService.list(lambda);
    return getDataTable(list);
  }

}
