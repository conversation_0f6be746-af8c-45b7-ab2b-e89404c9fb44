package com.ruoyi.scwt.identify.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@TableName("wc_user")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "文创链用户")
public class WcUser extends Model<WcUser> {
    /**
     * id
     */
//    @TableId(type = IdType.AUTO)
//    @ApiModelProperty(value="id")
//    private Long id;
    /**
     * 	上链数据ID
     */
    @TableId
    @ApiModelProperty(value="用户ID")
    private Long userId;
    /**
     * 	用户私钥
     */
    @ApiModelProperty(value="用户私钥")
    private String privateKey;
    /**
     * 用户公钥
     */
    @ApiModelProperty(value="用户公钥")
    private String publicKey;
    /**
     * 用户地址
     */
    @ApiModelProperty(value="用户地址")
    private String address;
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;
}
