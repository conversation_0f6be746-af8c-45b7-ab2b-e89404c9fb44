package com.ruoyi.scwt.identify.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.scwt.common.controller.AliIdentifyController;
import com.ruoyi.scwt.common.dto.CompanyIdentifyDto;
import com.ruoyi.scwt.common.dto.PersonIdentifyDto;
import com.ruoyi.scwt.common.util.AESUtils;
import com.ruoyi.scwt.common.util.DataDesensitizationUtil;
import com.ruoyi.scwt.common.util.IdNoEncryptMigration;
import com.ruoyi.scwt.common.util.SmsCodeUtils;
import com.ruoyi.scwt.file.entity.AttachmentInfo;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import com.ruoyi.scwt.file.util.OssUtil;
import com.ruoyi.scwt.identify.constant.IdentifyConstants;
import com.ruoyi.scwt.identify.dto.IdentifyDto;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.entity.LegalPerson;
import com.ruoyi.scwt.identify.enums.IdentifyEnum;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.identify.service.LegalPersonService;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.entity.RegisterOwner;
import com.ruoyi.scwt.works.service.EvidenceOwnerService;
import com.ruoyi.scwt.works.service.RegisterOwnerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 实名认证
 */
@RestController
@RequestMapping("/identify")
@Api(value = "identify", tags = "实名认证管理")
@Slf4j
public class IdentifyController extends BaseController {

    @Autowired
    private IdentifyService identifyService;
    @Autowired
    private EvidenceOwnerService evidenceOwnerService;
    @Autowired
    private RegisterOwnerService registerOwnerService;
    @Autowired
    private AliIdentifyController aliIdentifyController;
    @Autowired
    private OssUtil ossUtil;
    @Autowired
    private LegalPersonService legalPersonService;
    @Autowired
    private AttachmentInfoService attachmentInfoService;
    @Autowired
    private  SmsCodeUtils smsCodeUtils;
//    @Autowired
//    private Sm4Util sm4Util;

    @ApiOperation("新增实名认证")
    @PostMapping
    @Transactional(rollbackFor = Exception.class)
    public R save(@RequestBody @Validated Identify identify) {
        String idNo = identify.getIdNo();
//        identify.setIdNo(AESUtils.encrypt(idNo));
        Long userId = SecurityUtils.getUserId();
        if (null != identifyService.getIdentifyByUserId(userId)) {
            return R.fail("已实名认证，无需重新认证！");
        }
        if (!getLoginUser().getUser().getPhonenumber().equals(identify.getPhone())){
            return R.fail("手机号与登录手机号不一致");
        }
        if (IdentifyEnum.PERSON.getCode().equals(identify.getIdentifyType())) {
            smsCodeUtils.validateSmsCaptcha(identify.getPhone(), identify.getSmsCode());
            Identify people = identifyService.getOne(Wrappers.<Identify>lambdaQuery()
                    .eq(Identify::getIdName, identify.getIdName())
                    .eq(Identify::getIdNo, identify.getIdNo())
                    .eq(Identify::getStatusCd, IdentifyConstants.IDENTIFY_TAKE_EFFECT));
            if (ObjectUtil.isNotNull(people)) {
                return R.fail("该身份证号已实名认证");
            } else {
                identify.setStatusCd(IdentifyConstants.IDENTIFY_TAKE_EFFECT);
                PersonIdentifyDto personIdentifyDto = new PersonIdentifyDto(identify.getIdName(), idNo);
                R r = aliIdentifyController.eidIdentify(personIdentifyDto);
                if (!(Boolean) r.getData()) {
                    return R.fail(r.getMsg());
                }
            }
        } else {
            Identify people = identifyService.getOne(Wrappers.<Identify>lambdaQuery().eq(Identify::getIdName, identify.getIdName())
                    .eq(Identify::getIdNo, identify.getIdNo())
                    .eq(Identify::getLegalName, identify.getLegalName())
                    .eq(Identify::getStatusCd, IdentifyConstants.IDENTIFY_TAKE_EFFECT));
            if (ObjectUtil.isNotNull(people)) {
                return R.fail("该企业已经实名认证");
            } else {
                if (IdentifyEnum.BUSINESS_LICENSE.getCode().equals(identify.getIdType())) {
                    identify.setStatusCd(IdentifyConstants.IDENTIFY_TAKE_EFFECT);
                    CompanyIdentifyDto companyIdentifyDto = new CompanyIdentifyDto(identify.getIdName(), idNo, identify.getLegalName());
                    R r = aliIdentifyController.companyIdentify(companyIdentifyDto);
                    if (!(Boolean) r.getData()) {
                        return R.fail(r.getMsg());
                    }
                }
            }
        }
        identify.setStatusCd(IdentifyConstants.IDENTIFY_TAKE_EFFECT);
        identify.setCreateStaff(String.valueOf(userId));
        identify.setUserId(userId);
        identify.setCreateDate(LocalDateTime.now());
        identify.setStatusDate(LocalDateTime.now());
        // 添加存证著作权人
        EvidenceOwner evidenceOwner = new EvidenceOwner();
        BeanUtil.copyProperties(identify, evidenceOwner);
        evidenceOwner.setIdNo(idNo);
        evidenceOwner.setOwnerType(identify.getIdentifyType());
        evidenceOwner.setOwnerName(identify.getIdName());
        // 添加登记著作权人
        RegisterOwner registerOwner = new RegisterOwner();
        BeanUtil.copyProperties(evidenceOwner, registerOwner);
        BeanUtil.copyProperties(identify, registerOwner);
        registerOwner.setIdNo(idNo);
        registerOwner.setProvince(identify.getProvince());
        registerOwner.setCity(identify.getCity());
        registerOwner.setProvince(identify.getProvince());
        List<AttachmentInfo> attachmentInfoList = attachmentInfoService.getIdByPath(registerOwner.getIdCard());
        registerOwner.setIdCard(attachmentInfoList.stream().map(AttachmentInfo::getAttachId).map(String::valueOf).collect(Collectors.joining(",")));

        evidenceOwnerService.insert(evidenceOwner);
        registerOwnerService.insert(registerOwner);
        identifyService.save(identify);
        identify.setIdNo(idNo);
        identifyService.sopForeign(identify);
        return R.ok("实名认证成功");
    }

    @ApiOperation("新增法人实名认证")
    @PostMapping("/legalPerson")
    @Transactional(rollbackFor = Exception.class)
    public R save(@RequestBody @Validated LegalPerson legalPerson) {
        Long userId = SecurityUtils.getUserId();
        Identify identify = identifyService.getIdentifyByUserId(userId);
        if (!identify.getIdName().equals(legalPerson.getIdName())){
            return R.fail("法人名称不一致");
        }
        if (ObjectUtil.isEmpty(identify)) {
            return R.fail("请先完成企业认证");
        } else if (identify.getStatusCd().equals(IdentifyConstants.IDENTIFY_TAKE_EFFECT)) {
            return R.ok("已实名认证，无需重新认证！");
        } else if (identify.getStatusCd().equals(IdentifyConstants.IDENTIFY_LEGAL_PERSON)) {
            PersonIdentifyDto personIdentifyDto = new PersonIdentifyDto(legalPerson.getIdName(), legalPerson.getIdNo());
            R r = aliIdentifyController.eidIdentify(personIdentifyDto);
            if (!(Boolean) r.getData()) {
                return R.fail(r.getMsg());
            }
            legalPerson.setIdentifyId(identify.getIdentifyId());
            identify.setStatusCd(IdentifyConstants.IDENTIFY_TAKE_EFFECT);
//            identify.setLegalName(legalPerson.getIdName());
            identify.setPhone(legalPerson.getPhone());
            identifyService.updateById(identify);
            legalPersonService.save(legalPerson);
//            eventPublisher.publishEvent(identify);
            return R.ok("法人实名认证成功");
        }
        return R.fail("非法操作");
    }

    /**
     * 查询登录用户认证信息
     *
     * @return R
     */
    @ApiOperation(value = "查询登录用户认证信息", notes = "查询登录用户认证信息")
    @GetMapping
    public R getMyIdentifyInfo() {
        Long userId = getUserId();
        List<Identify> identifyList = identifyService.getIdentifyVo(userId);
        if (identifyList.size() > 0) {
            Identify identify = identifyList.get(0);
            identify.setIdName(DataDesensitizationUtil.replaceLeft(identify.getIdName(), 1));
//            identify.setIdNo(DataDesensitizationUtil.replaceLeftRight(AESUtils.decrypt(identify.getIdNo()), 6, 4));
            identify.setIdNo(DataDesensitizationUtil.replaceLeftRight(identify.getIdNo(), 6, 4));
            identify.setPhone(DataDesensitizationUtil.replaceLeftRight(identify.getPhone(), 3, 4));
            identify.setAddress(DataDesensitizationUtil.replaceLeft(identify.getAddress(), 6));
            if (StrUtil.isNotEmpty(identify.getLegalName())) {
                identify.setLegalName(DataDesensitizationUtil.replaceLeft(identify.getLegalName(), 1));
            }
//            LegalPerson legalPerson= legalPersonService.getByIdentifyId(identify.getIdentifyId());
//            if (ObjectUtil.isNotNull(legalPerson)) {
//                legalPerson.setPhone(DataDesensitizationUtil.replaceLeftRight(legalPerson.getPhone(), 3, 4));
//                legalPerson.setIdNo(DataDesensitizationUtil.replaceLeftRight(legalPerson.getIdNo(), 6, 4));
//                legalPerson.setAddress(DataDesensitizationUtil.replaceLeft(legalPerson.getAddress(), 6));
//                legalPerson.setIdName(DataDesensitizationUtil.replaceLeft(legalPerson.getIdName(), 1));
//                legalPerson.setIdCardFace(ossUtil.getFileUrl(legalPerson.getIdCardFace()));
//                legalPerson.setIdCardBack(ossUtil.getFileUrl(legalPerson.getIdCardBack()));
//                identify.setLegalPerson(legalPerson);
//            }
            if (StrUtil.isNotEmpty(identify.getIdCard())) {
                String[] idCard = identify.getIdCard().split(",");
                for (String s : idCard) {
                    ossUtil.getFileUrl(s);
                }
                identify.setIdCard(String.join(",", idCard));
            }
            return R.ok(identify, "已实名");
        } else {
            return R.ok("未实名");
        }
    }

    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public R getMyIdentifyInfoPage(IdentifyDto identifyDto) {
//        Long userId = getUserId();
        LambdaQueryWrapper<Identify> queryWrapper = Wrappers.<Identify>lambdaQuery()
                .like(StrUtil.isNotEmpty(identifyDto.getIdName()), Identify::getIdName, identifyDto.getIdName())
                .eq( Identify::getStatusCd, IdentifyConstants.IDENTIFY_TAKE_EFFECT)
                .eq(StringUtils.isNotEmpty(identifyDto.getIdentifyType()), Identify::getIdentifyType, identifyDto.getIdentifyType())
                .orderByDesc(Identify::getCreateDate);
        startPage();
        List<Identify> identifyList = identifyService.list(queryWrapper);
        if (identifyList.size() > 0) {
            for (int i = 0; i < identifyList.size(); i++) {
                Identify identify = identifyList.get(i);
                identify.setIdName(DataDesensitizationUtil.replaceLeft(identify.getIdName(), 1));
//                identify.setIdNo(DataDesensitizationUtil.replaceLeftRight(AESUtils.decrypt(identify.getIdNo()), 6, 4));
                identify.setIdNo(DataDesensitizationUtil.replaceLeftRight(identify.getIdNo(), 6, 4));
                identify.setPhone(DataDesensitizationUtil.replaceLeftRight(identify.getPhone(), 3, 4));
                identify.setAddress(DataDesensitizationUtil.replaceLeft(identify.getAddress(), 6));
                if (StrUtil.isNotEmpty(identify.getLegalName())) {
                    identify.setLegalName(DataDesensitizationUtil.replaceLeft(identify.getLegalName(), 1));
                }
                if(StrUtil.isNotEmpty(identify.getRegistrationPlace())){
                    identify.setRegistrationPlace(DataDesensitizationUtil.replaceLeft(identify.getRegistrationPlace(), 6));
                }
            }
        }
        return R.ok(getDataTable(identifyList));
    }


    @ApiOperation(value = "分页查询", notes = "分页查询")
    @PostMapping("/page/export")
    public void getIdentigyExport(HttpServletResponse response, IdentifyDto identifyDto) {
//        Long userId = getUserId();
        LambdaQueryWrapper<Identify> queryWrapper = Wrappers.<Identify>lambdaQuery()
                .like(StrUtil.isNotEmpty(identifyDto.getIdName()), Identify::getIdName, identifyDto.getIdName())
                .eq( Identify::getStatusCd, IdentifyConstants.IDENTIFY_TAKE_EFFECT)
                .eq(StringUtils.isNotEmpty(identifyDto.getIdentifyType()), Identify::getIdentifyType, identifyDto.getIdentifyType())
                .orderByDesc(Identify::getCreateDate);
//        startPage();
        List<Identify> identifyList = identifyService.list(queryWrapper);
        if (identifyList.size() > 0) {
            for (int i = 0; i < identifyList.size(); i++) {
                Identify identify = identifyList.get(i);
                identify.setIdName(DataDesensitizationUtil.replaceLeft(identify.getIdName(), 1));
//                identify.setIdNo(DataDesensitizationUtil.replaceLeftRight(AESUtils.decrypt(identify.getIdNo()), 6, 4));
                identify.setIdNo(DataDesensitizationUtil.replaceLeftRight(identify.getIdNo(), 6, 4));
                identify.setPhone(DataDesensitizationUtil.replaceLeftRight(identify.getPhone(), 3, 4));
                identify.setAddress(DataDesensitizationUtil.replaceLeft(identify.getAddress(), 6));
                if (StrUtil.isNotEmpty(identify.getLegalName())) {
                    identify.setLegalName(DataDesensitizationUtil.replaceLeft(identify.getLegalName(), 1));
                }
                if(StrUtil.isNotEmpty(identify.getRegistrationPlace())){
                    identify.setRegistrationPlace(DataDesensitizationUtil.replaceLeft(identify.getRegistrationPlace(), 6));
                }
            }
        }
        ExcelUtil<Identify> util = new ExcelUtil<Identify>(Identify.class);
        util.exportExcel(response, identifyList, "订单数据");
    }

    @ApiOperation(value = "账号注销", notes = "账号注销")
    @DeleteMapping ("/logout")
    public R userLogout(String smsCode) {
        String phonenumber = getLoginUser().getUser().getPhonenumber();
        smsCodeUtils.validateSmsCaptcha(phonenumber, smsCode);
        return identifyService.userLogout(getUserId());
    }

    /**
     * 判断是否实名认证
     */
    @ApiOperation("判断是否实名认证")
    @GetMapping("/isIdentify")
    public R isIdentify() {
        return R.ok(identifyService.isIdentify(String.valueOf(getUserId())));
    }

    /**
     * 获取用户链上地址
     */

    @ApiOperation("获取用户链上地址")
    @GetMapping("/getUserAddress")
    public R getUserAddress() {
        return R.ok(identifyService.getUserAddress(getUserId()));
    }

}
