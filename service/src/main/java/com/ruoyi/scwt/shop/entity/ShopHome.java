package com.ruoyi.scwt.shop.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;


@Data
@ApiModel
@TableName("shop_home")
@EqualsAndHashCode(callSuper = true)
public class ShopHome extends Model<ShopHome> {
private static final long serialVersionUID = 1L;

  /**
   * 店铺ID
   */
  @ApiModelProperty(value = "店铺ID")
  private Long shopId;

  /**
   * 标签ID
   */
  @ApiModelProperty(value = "排序")
  private Long sort;
  @ApiModelProperty(hidden = true)
  private LocalDateTime createDate;

}
