package com.ruoyi.scwt.shop.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.scwt.asset.constant.AssetConstants;
import com.ruoyi.scwt.asset.dto.AssetStatusDto;
import com.ruoyi.scwt.asset.entity.Asset;
import com.ruoyi.scwt.asset.entity.AssetZoneShop;
import com.ruoyi.scwt.asset.mapper.AssetZoneShopMapper;
import com.ruoyi.scwt.asset.service.AssetService;
import com.ruoyi.scwt.asset.service.AssetZoneShopService;
import com.ruoyi.scwt.asset.vo.AssetVo;
import com.ruoyi.scwt.common.entity.SysLabel;
import com.ruoyi.scwt.common.service.SysLabelService;
import com.ruoyi.scwt.common.service.SysRegionService;
import com.ruoyi.scwt.common.util.DataDesensitizationUtil;
import com.ruoyi.scwt.common.util.SmsCodeUtils;
import com.ruoyi.scwt.file.util.OssUtil;
import com.ruoyi.scwt.identify.constant.IdentifyConstants;
import com.ruoyi.scwt.identify.entity.Identify;
import com.ruoyi.scwt.identify.enums.IdentifyEnum;
import com.ruoyi.scwt.identify.service.IdentifyService;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.entify.Union;
import com.ruoyi.scwt.pay.service.OrderSubDetailService;
import com.ruoyi.scwt.pay.service.UnionService;
import com.ruoyi.scwt.pay.service.impl.OrderSubDetailServiceImpl;
import com.ruoyi.scwt.pay.vo.OrderVo;
import com.ruoyi.scwt.shop.constant.ShopConstants;
import com.ruoyi.scwt.shop.dto.ShopInfoDto;
import com.ruoyi.scwt.shop.dto.ShopInfoStatusDto;
import com.ruoyi.scwt.shop.entity.ShopHome;
import com.ruoyi.scwt.shop.entity.ShopInfo;
import com.ruoyi.scwt.shop.entity.ShopLabelRel;
import com.ruoyi.scwt.shop.mapper.ShopLabelRelMapper;
import com.ruoyi.scwt.shop.service.ShopHomeService;
import com.ruoyi.scwt.shop.service.ShopInfoService;
import com.ruoyi.scwt.shop.service.ShopLabelRelService;
import com.ruoyi.scwt.shop.vo.ShopInfoTransferDataVo;
import com.ruoyi.scwt.shop.vo.ShopInfoVo;
import com.ruoyi.scwt.works.constant.OwnerConstants;
import com.ruoyi.scwt.works.dto.EvidenceOwnerDto;
import com.ruoyi.scwt.works.entity.EvidenceOwner;
import com.ruoyi.scwt.works.service.EvidenceOwnerService;
import com.ruoyi.scwt.zone.entity.ZoneHome;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 实名认证
 */
@RestController
@AllArgsConstructor
@RequestMapping("/shop")
@Api(value = "shop", tags = "店铺")
@Slf4j
public class ShopInfoController extends BaseController {

    private final ShopInfoService shopInfoService;

    private final AssetService assetService;

    private final IdentifyService identifyService;

    private final AssetZoneShopService assetZoneShopService;

    private final ShopHomeService shopHomeService;

    private final OssUtil ossUtil;

    private final OrderSubDetailService orderSubDetailService;

    private final SmsCodeUtils smsCodeUtils;

    private final UnionService unionService;

    /**
     * 分页查询
     *
     * @param dto 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询-不需要登录", notes = "分页查询-不需要登录")
    @GetMapping("/home")
    @Anonymous
    public R getShopHome(ShopInfoDto dto) {
        List<ShopInfo> list = new ArrayList<>();
        if (dto.getIsHome()) {
            List<ShopHome> shopHomes = shopHomeService.list(Wrappers.<ShopHome>lambdaQuery().orderByDesc(ShopHome::getCreateDate));
            List<Long> shopIds = shopHomes.stream().map(ShopHome::getShopId).collect(Collectors.toList());
            for (Long shopId : shopIds) {
                ShopInfo one = shopInfoService.getOne(Wrappers.<ShopInfo>lambdaQuery().eq(ShopInfo::getShopId, shopId).eq(ShopInfo::getStatusCd, ShopConstants.SHOP_TAKE_EFFECT));
                if (ObjectUtil.isNotEmpty(one)) list.add(one);
            }
        } else {
//            List<Long> shopIds = null;
//            if (StringUtils.isNotEmpty(dto.getLabelIds())) {
//                List<ShopLabelRel> shopLabelRels = shopLabelRelService.list(Wrappers.<ShopLabelRel>query().lambda().in(ShopLabelRel::getLabelId, StrUtil.split(dto.getLabelIds(), ",")));
//                shopIds = shopLabelRels.stream().map(ShopLabelRel::getShopId).collect(Collectors.toList());
//            }
            LambdaQueryWrapper<ShopInfo> lambda = Wrappers.<ShopInfo>query().lambda()
                    .like(StringUtils.isNotEmpty(dto.getShopName()), ShopInfo::getShopName, dto.getShopName())
                    .eq(ShopInfo::getStatusCd, ShopConstants.SHOP_TAKE_EFFECT)
                    .like(ObjectUtil.isNotEmpty(dto.getLabelName()), ShopInfo::getLabelName, dto.getLabelName())
                    .orderByDesc(ShopInfo::getCreateDate);
            startPage();
            list = shopInfoService.list(lambda);
        }
        List<ShopInfoVo> voList = list.stream().map(item -> {
            ShopInfoVo shopInfoVo = new ShopInfoVo();
            BeanUtils.copyProperties(item, shopInfoVo);
//            shopInfoVo.setLabels(sysLabelService.getListByShopId(item.getShopId()));
            shopInfoVo.setShopCover(ossUtil.getFileUrl(item.getShopCover()));
            shopInfoVo.setShopLogo(ossUtil.getFileUrl(item.getShopLogo()));
            return shopInfoVo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);
    }

    /**
     * 分页查询
     *
     * @param shopId 分页对象
     * @return
     */
    @ApiOperation(value = "店铺资产分页查询", notes = "店铺资产分页查询")
    @GetMapping("/asset/list")
    @Anonymous
    public R getShopAssetList(Long shopId, String assetName, String assetType, String businessCategory, String transactionType, String statusCd) {
        if (ObjectUtil.isEmpty(shopId)) {
            shopId = shopInfoService.getOne(Wrappers.<ShopInfo>lambdaQuery().eq(ShopInfo::getCreateStaff, getUserId()).ne(ShopInfo::getStatusCd, ShopConstants.SHOP_DELETE)).getShopId();
        }
        List<AssetZoneShop> listByShopId = assetZoneShopService.getListByShopId(shopId);
        if (listByShopId.size() == 0) {
            return R.fail("该店铺暂无资产");
        }
        List<Long> assetIds = listByShopId.stream().map(AssetZoneShop::getAssetId).collect(Collectors.toList());
        LambdaQueryWrapper<Asset> lambda = Wrappers.<Asset>query().lambda()
//                .eq( Asset::getStatusCd, AssetConstants.ASSET_ON_SHELF)
                .like(StringUtils.isNotEmpty(assetName), Asset::getAssetName, assetName)
                .eq(StringUtils.isNotEmpty(assetType), Asset::getAssetType, assetType)
                .eq(StringUtils.isNotEmpty(statusCd), Asset::getStatusCd, statusCd)
                .ne(StringUtils.isEmpty(statusCd), Asset::getStatusCd, AssetConstants.ASSET_DELETED)
                .eq(StringUtils.isNotEmpty(businessCategory), Asset::getBusinessCategory, businessCategory)
                .eq(StringUtils.isNotEmpty(transactionType), Asset::getTransactionType, transactionType)
                .in(Asset::getAssetId, assetIds)
                .orderByDesc(Asset::getCreateDate);
        startPage();
        List<Asset> list = assetService.list(lambda);
        List<AssetVo> voList = list.stream().map(item -> {
            AssetVo assetVo = new AssetVo();
            BeanUtils.copyProperties(item, assetVo);
//            assetVo.setLabels(sysLabelService.getLabelListByAssetId(assetVo.getAssetId()));
            assetVo.setAssetCover(ossUtil.getFileUrl(item.getAssetCover()));
            ShopInfoVo shopInfoVo = shopInfoService.getShopByAssetId(assetVo.getAssetId());
            assetVo.setShopInfoVo(shopInfoVo);
            return assetVo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);

    }

    /**
     * 分页查询
     *
     * @param dto 分页对象
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public R getShopPage(ShopInfoDto dto) {
//        List<Long> shopIds = null;
//        if (StringUtils.isNotEmpty(dto.getLabelIds())) {
//            List<ShopLabelRel> shopLabelRels = shopLabelRelService.list(Wrappers.<ShopLabelRel>query().lambda().in(ShopLabelRel::getLabelId, StrUtil.split(dto.getLabelIds(), ",")));
//            shopIds = shopLabelRels.stream().map(ShopLabelRel::getShopId).collect(Collectors.toList());
//        }
        LambdaQueryWrapper<ShopInfo> lambda = Wrappers.<ShopInfo>query().lambda()
                .like(StringUtils.isNotEmpty(dto.getShopName()), ShopInfo::getShopName, dto.getShopName())
                .eq(StringUtils.isNotEmpty(dto.getStatusCd()), ShopInfo::getStatusCd, dto.getStatusCd())
                .ne(StringUtils.isEmpty(dto.getStatusCd()), ShopInfo::getStatusCd, ShopConstants.SHOP_DELETE)
//                .eq(!SecurityUtils.isManager(), ShopInfo::getCreateStaff, getUserId())
                .in(ObjectUtil.isNotEmpty(dto.getLabelName()), ShopInfo::getLabelName, dto.getLabelName())
                .orderByDesc(ShopInfo::getCreateDate);
        startPage();
        List<ShopInfo> list = shopInfoService.list(lambda);
        List<ShopInfoVo> voList = list.stream().map(item -> {
            ShopInfoVo shopInfoVo = new ShopInfoVo();
            BeanUtils.copyProperties(item, shopInfoVo);
            shopInfoVo.setForSaleNum(Math.toIntExact(assetService.getForSaleNumByShopId(item.getShopId())));
            Union union = unionService.getOne(Wrappers.<Union>lambdaQuery().eq(Union::getShopId, item.getShopId()));
            if (ObjectUtil.isNotEmpty(union) && StrUtil.isNotEmpty(union.getMerNo()))shopInfoVo.setMid(union.getMerNo());
            return shopInfoVo;
        }).collect(Collectors.toList());
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(voList);
        return R.ok(dataTable);
    }


    @ApiOperation(value = "Excel导出", notes = "Excel导出")
    @PostMapping("/page/export")
    public void getShopPage(HttpServletResponse response, ShopInfoDto dto) {
//        List<Long> shopIds = null;
//        if (StringUtils.isNotEmpty(dto.getLabelIds())) {
//            List<ShopLabelRel> shopLabelRels = shopLabelRelService.list(Wrappers.<ShopLabelRel>query().lambda().in(ShopLabelRel::getLabelId, StrUtil.split(dto.getLabelIds(), ",")));
//            shopIds = shopLabelRels.stream().map(ShopLabelRel::getShopId).collect(Collectors.toList());
//        }
        LambdaQueryWrapper<ShopInfo> lambda = Wrappers.<ShopInfo>query().lambda()
                .like(StringUtils.isNotEmpty(dto.getShopName()), ShopInfo::getShopName, dto.getShopName())
                .eq(StringUtils.isNotEmpty(dto.getStatusCd()), ShopInfo::getStatusCd, dto.getStatusCd())
                .ne(StringUtils.isEmpty(dto.getStatusCd()), ShopInfo::getStatusCd, ShopConstants.SHOP_DELETE)
//                .eq(!SecurityUtils.isManager(), ShopInfo::getCreateStaff, getUserId())
                .in(ObjectUtil.isNotEmpty(dto.getLabelName()), ShopInfo::getLabelName, dto.getLabelName())
                .orderByDesc(ShopInfo::getCreateDate);
//        startPage();
        List<ShopInfo> list = shopInfoService.list(lambda);
        List<ShopInfoVo> voList = list.stream().map(item -> {
            ShopInfoVo shopInfoVo = new ShopInfoVo();
            BeanUtils.copyProperties(item, shopInfoVo);
            shopInfoVo.setForSaleNum(Math.toIntExact(assetService.getForSaleNumByShopId(item.getShopId())));
            Union union = unionService.getOne(Wrappers.<Union>lambdaQuery().eq(Union::getShopId, item.getShopId()));
            if (ObjectUtil.isNotEmpty(union) && StrUtil.isNotEmpty(union.getMerNo()))shopInfoVo.setMid(union.getMerNo());
            return shopInfoVo;
        }).collect(Collectors.toList());
        ExcelUtil<ShopInfoVo> util = new ExcelUtil<ShopInfoVo>(ShopInfoVo.class);
        util.exportExcel(response, voList, "订单数据");
    }

    /**
     * 新增
     *
     * @param shopInfo
     * @return R
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/basicInfo")
    public R save(@Validated @RequestBody ShopInfo shopInfo) {
        ShopInfo one = shopInfoService.getOne(Wrappers.<ShopInfo>query().lambda().eq(ShopInfo::getCreateStaff, SecurityUtils.getUserId()).ne(ShopInfo::getStatusCd, ShopConstants.SHOP_DELETE));
        if (ObjectUtil.isNotEmpty(one)) {
            return R.fail("您已创建店铺，请勿重复创建");
        }
        String userId = String.valueOf(SecurityUtils.getUserId());
        if (!identifyService.isIdentify(userId)) {
            return R.fail("请先完成实名认证");
        }
        Long count = shopInfoService.getRepeatName(shopInfo.getShopName());
        if (count > 0) {
            return R.fail("该店铺名称已存在");
        }
        shopInfo.setUnionStatus(OrderConstants.UNION_TOBE_CERTIFIED);
        shopInfo.setCreateStaff(userId);
        shopInfo.setStatusCd(ShopConstants.SHOP_TOBE_AUDIT);
        shopInfo.setCreateDate(LocalDateTime.now());
        shopInfo.setStatusDate(LocalDateTime.now());
        shopInfoService.save(shopInfo);
//        shopLabelRelService.insertLabelRel(shopInfo.getShopId(), shopInfo.getLabelIds());
        return R.ok("店铺信息新增成功！");
    }

    /**
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping("/center")
    public R getById() {
        Long userId = getUserId();
        ShopInfo one = shopInfoService.getOne(Wrappers.<ShopInfo>query().lambda().eq(ShopInfo::getCreateStaff, userId).ne(ShopInfo::getStatusCd, ShopConstants.SHOP_DELETE));
        if (ObjectUtil.isEmpty(one)) {
            return R.fail("请先完善店铺信息");
        }
        ShopInfo shopInfo = shopInfoService.getById(one.getShopId());
        ShopInfoVo shopInfoVo = new ShopInfoVo();
        BeanUtils.copyProperties(shopInfo, shopInfoVo);
        Long forSaleNum = assetService.getForSaleNumByShopId(shopInfoVo.getShopId());
        shopInfoVo.setForSaleNum(Math.toIntExact(forSaleNum));
        Long authorizeNum = assetService.getAssetTypeNumByShopId(shopInfoVo.getShopId(), AssetConstants.ASSET_COPYRIGHT_SALE);
        shopInfoVo.setAuthorizeNum(Math.toIntExact(authorizeNum));
        Long transferNum = assetService.getAssetTypeNumByShopId(shopInfoVo.getShopId(), AssetConstants.ASSET_TRANSFER_SALE);
        shopInfoVo.setTransferNum(Math.toIntExact(transferNum));
        Long bargainingNum = assetService.getAssetTypeNumByShopId(shopInfoVo.getShopId(), AssetConstants.ASSET_BARGAINING_SALE);
        shopInfoVo.setBargainingNum(Math.toIntExact(bargainingNum));
        shopInfoVo.setShopCover(ossUtil.getFileUrl(shopInfoVo.getShopCover()));
        shopInfoVo.setShopLogo(ossUtil.getFileUrl(shopInfoVo.getShopLogo()));
        if (StringUtils.isNotEmpty(shopInfoVo.getShopQrcode())) {
            shopInfoVo.setShopQrcode(ossUtil.getFileUrl(shopInfoVo.getShopQrcode()));
        }
        return R.ok(shopInfoVo);
    }

    /**
     * 通过id查询
     *
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping
    @Anonymous
    public R getById(Long id) {
        ShopInfo shopInfo = shopInfoService.getById(id);
        ShopInfoVo shopInfoVo = new ShopInfoVo();
        BeanUtils.copyProperties(shopInfo, shopInfoVo);
        Long forSaleNum = assetService.getForSaleNumByShopId(shopInfoVo.getShopId());
        shopInfoVo.setForSaleNum(Math.toIntExact(forSaleNum));
        Long authorizeNum = assetService.getAssetTypeNumByShopId(shopInfoVo.getShopId(), AssetConstants.ASSET_COPYRIGHT_SALE);
        shopInfoVo.setAuthorizeNum(Math.toIntExact(authorizeNum));
        Long transferNum = assetService.getAssetTypeNumByShopId(shopInfoVo.getShopId(), AssetConstants.ASSET_TRANSFER_SALE);
        shopInfoVo.setTransferNum(Math.toIntExact(transferNum));
        Long bargainingNum = assetService.getAssetTypeNumByShopId(shopInfoVo.getShopId(), AssetConstants.ASSET_BARGAINING_SALE);
        shopInfoVo.setBargainingNum(Math.toIntExact(bargainingNum));
        if (StringUtils.isNotEmpty(shopInfoVo.getShopCover())) {
            shopInfoVo.setShopCover(ossUtil.getFileUrl(shopInfoVo.getShopCover()));
        }
        shopInfoVo.setShopLogo(ossUtil.getFileUrl(shopInfoVo.getShopLogo()));
        if (StringUtils.isNotEmpty(shopInfoVo.getShopQrcode())) {
            shopInfoVo.setShopQrcode(ossUtil.getFileUrl(shopInfoVo.getShopQrcode()));
        }
        return R.ok(shopInfoVo);
    }

    @ApiOperation(value = "新增/取消推荐店铺", notes = "新增/取消推荐店铺")
    @PutMapping("/shopHome")
    public R shopHome(Long shopId) {
        ShopHome shopHome = shopHomeService.getOne(Wrappers.<ShopHome>query().lambda().eq(ShopHome::getShopId, shopId));
        if (ObjectUtil.isEmpty(shopHome)) {
            shopHome = new ShopHome();
            shopHome.setShopId(shopId);
            shopHome.setCreateDate(LocalDateTime.now());
            shopHome.setSort(1L);
            shopHomeService.save(shopHome);
        } else {
            shopHomeService.remove(Wrappers.<ShopHome>query().lambda().eq(ShopHome::getShopId, shopId));
        }
        return R.ok("操作成功");
    }

    /**
     * 修改
     *
     * @param shopInfo
     * @return R
     */
    @ApiOperation(value = "修改", notes = "修改")
    @PutMapping
    public R updateById(@Validated @RequestBody ShopInfo shopInfo) {
        String userId = String.valueOf(SecurityUtils.getUserId());
        shopInfo.setUpdateStaff(userId);
        shopInfo.setUpdateDate(LocalDateTime.now());
        shopInfo.setStatusCd(ShopConstants.SHOP_TOBE_AUDIT);
        shopInfo.setStatusDate(LocalDateTime.now());
        shopInfoService.updateById(shopInfo);
//        shopLabelRelService.remove(Wrappers.<ShopLabelRel>query().lambda().eq(ShopLabelRel::getShopId, shopInfo.getShopId()));
//        shopLabelRelService.insertLabelRel(shopInfo.getShopId(), shopInfo.getLabelIds());
        return R.ok("店铺修改成功！");
    }

    @ApiOperation(value = "状态修改", notes = "状态修改")
    @PutMapping("/updateStatus")
    public R updateStatusById(@Validated @RequestBody ShopInfoStatusDto dto) {
        if (!StringUtils.equalsAny(dto.getStatusCd(), ShopConstants.SHOP_TAKE_EFFECT, ShopConstants.SHOP_REJECT, ShopConstants.SHOP_TOBE_AUDIT, ShopConstants.SHOP_DOWN)) {
            return R.fail("错误状态");
        }
        ShopInfo one = shopInfoService.getOne(Wrappers.<ShopInfo>query().lambda().eq(ShopInfo::getShopId, dto.getShopId()));
        if (ObjectUtil.isEmpty(one)) {
            return R.fail("店铺不存在");
        }
        if (dto.getStatusCd().equals(ShopConstants.SHOP_TOBE_AUDIT) && !one.getStatusCd().equals(ShopConstants.SHOP_DOWN)
                || dto.getStatusCd().equals(ShopConstants.SHOP_REJECT) && !one.getStatusCd().equals(ShopConstants.SHOP_TOBE_AUDIT)
                || dto.getStatusCd().equals(ShopConstants.SHOP_TAKE_EFFECT) && !one.getStatusCd().equals(ShopConstants.SHOP_TOBE_AUDIT)
                || dto.getStatusCd().equals(ShopConstants.SHOP_DOWN) && !one.getStatusCd().equals(ShopConstants.SHOP_TAKE_EFFECT)) {
            return R.fail("店铺状态不支持修改");
        }
        one.setStatusCd(dto.getStatusCd());
        one.setStatusDate(LocalDateTime.now());
        one.setUpdateStaff(String.valueOf(SecurityUtils.getUserId()));
        one.setUpdateDate(LocalDateTime.now());
        if (dto.getStatusCd().equals(ShopConstants.SHOP_DOWN) || dto.getStatusCd().equals(ShopConstants.SHOP_REJECT)) {
            one.setDownReason(dto.getDownReason());
        }
        if (dto.getStatusCd().equals(ShopConstants.SHOP_TAKE_EFFECT) || dto.getStatusCd().equals(ShopConstants.SHOP_DOWN)) {
            assetService.setShopStatusCd(one.getShopId(), dto.getStatusCd(),null);
        }
        return R.ok(shopInfoService.updateById(one));
    }

    @ApiOperation(value = "查询店铺名称列表", notes = "查询店铺名称列表")
    @GetMapping("/nameList")
    @Anonymous
    public R getShopNameList() {
        List<ShopInfo> list = shopInfoService.list(Wrappers.<ShopInfo>lambdaQuery()
                .select(ShopInfo::getShopName, ShopInfo::getShopId)
                .eq(ShopInfo::getStatusCd, ShopConstants.SHOP_TAKE_EFFECT));
        return R.ok(list);
    }


    @ApiOperation(value = "交易数据", notes = "交易数据")
    @GetMapping("/transferData")
    public R transferData() {
        ShopInfoTransferDataVo vo = new ShopInfoTransferDataVo();
        List<Long> assetIds = new ArrayList<>();
        ShopInfo one = shopInfoService.getOne(Wrappers.<ShopInfo>lambdaQuery().eq(ShopInfo::getCreateStaff, getUserId()));
        //在售资产
        if (one.getStatusCd().equals(ShopConstants.SHOP_TAKE_EFFECT)) {
            List<AssetZoneShop> assetZoneShopList = assetZoneShopService.getListByShopId(one.getShopId());
            if (!assetZoneShopList.isEmpty()) {
                assetIds = assetZoneShopList.stream().map(AssetZoneShop::getAssetId).collect(Collectors.toList());
                long count = assetService.count(Wrappers.<Asset>lambdaQuery()
                        .eq(Asset::getStatusCd, AssetConstants.ASSET_ON_SHELF)
                        .in(Asset::getAssetId, assetIds));
                vo.setForSaleNum(String.valueOf(count));
            }
        }
        //销售收入
        if (assetIds.size() > 0) {
            String salesRevenue = orderSubDetailService.getSalesRevenue(assetIds,null);
            vo.setSalesRevenue(salesRevenue);
        }
        //7天销售收入
        Map<String, String> sevenSalesRevenue = orderSubDetailService.getSevenSalesRevenue(assetIds);
        vo.setSevenSalesRevenue(sevenSalesRevenue);
        return R.ok(vo);
    }


    @ApiOperation(value = "店铺注销", notes = "店铺注销")
    @DeleteMapping ("/logout")
    public R shopLogout(Long shopId) {
//        String phonenumber = getLoginUser().getUser().getPhonenumber();
//        smsCodeUtils.validateSmsCaptcha(phonenumber, smsCode);
        return shopInfoService.shopLogout(shopId);
    }
}
