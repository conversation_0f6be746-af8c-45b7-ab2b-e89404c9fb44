package com.ruoyi.scwt.shop.vo;

import com.ruoyi.scwt.common.entity.SysLabel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
public class ShopInfoTransferDataVo {

    @ApiModelProperty(value="店铺ID")
    private Long shopId;


    @ApiModelProperty(value="在售资源数量")
    private String forSaleNum="0";


    @ApiModelProperty(value="销售收入")
    private String salesRevenue="0.00";


    @ApiModelProperty(value="进7天销售收入")
    private Map<String,String> sevenSalesRevenue;

}
