package com.ruoyi.scwt.shop.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.scwt.common.entity.SysLabel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @date 2021-10-12 14:27:33
 */
@Data
public class ShopInfoVo {

    @ApiModelProperty(value="店铺ID")
    private Long shopId;

    @ApiModelProperty(value="店铺名称")
    @Excel(name = "店铺名称", prompt = "店铺名称")
    private String shopName;

    @ApiModelProperty(value="店铺类型")
    @Excel(name = "交易类型", dictType = "shop_type")
    private String shopType;

    @ApiModelProperty(value="店铺封面")
    private String shopCover;

    @ApiModelProperty(value="店铺logo")
    private String shopLogo;

    @ApiModelProperty(value="店铺简介")
    @Excel(name = "店铺简介", prompt = "店铺简介")
    private String shopAbstract;

    @ApiModelProperty(value="店铺属地")
    private String belongPlace;
    @ApiModelProperty(value="店铺联系电话")
    private String shopPhone;
    @ApiModelProperty(value="店铺邮箱")
    private String shopEmail;
    @ApiModelProperty(value="店铺二维码")
    private String shopQrcode;

    @ApiModelProperty(value="状态")
    @Excel(name = "状态", readConverterExp = "2000=正常,2300=待审核,2301=审核驳回,2800=下架,2900=删除")
    private String statusCd;

    @ApiModelProperty(value="标签")
    private List<SysLabel> labelList;

    @ApiModelProperty(value="在售资源数量")
    @Excel(name = "在售资源数量", prompt = "在售资源数量")
    private int forSaleNum;

    @ApiModelProperty(value="版权授权数量")
    private int authorizeNum;

    @ApiModelProperty(value="版权转让数量")
    private int transferNum;

    @ApiModelProperty(value="议价数量")
    private int bargainingNum;

    @ApiModelProperty(value="历史成交数量")
    private int dealNum;

    @ApiModelProperty(value="近30天成交数量")
    private int thirtyDealNum;

//    @ApiModelProperty(value="标签列表")
//    private List<SysLabel> labels;

    @ApiModelProperty(value="标签，多个以逗号隔开",hidden = true)
    private String labelName;

    @ApiModelProperty(value="创建人",hidden = true)
    private String createStaff;

    @ApiModelProperty(value="创建时间",hidden = true)
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;
    @ApiModelProperty(value = "下架原因")
    private String downReason;
    @ApiModelProperty(value = "认证状态")
    private String unionStatus;
    @ApiModelProperty(value = "商户编号")
    @Excel(name = "商户编号", prompt = "商户编号")
    private String mid;
}
