package com.ruoyi.scwt.pay.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.scwt.common.util.DataDesensitizationUtil;
import com.ruoyi.scwt.common.util.ImageUtil;
import com.ruoyi.scwt.pay.entify.Union;
import com.ruoyi.scwt.pay.entify.UnionPicUpload;
import com.ruoyi.scwt.pay.service.UnionService;
import com.ruoyi.scwt.shop.constant.ShopConstants;
import com.ruoyi.scwt.shop.entity.ShopInfo;
import com.ruoyi.scwt.shop.service.ShopInfoService;
import com.ruoyi.scwt.sop.dto.ChinaumsMerchantComplexUploadDto;
import com.ruoyi.scwt.sop.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.awt.*;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 实名认证
 */
@RestController
@RequestMapping("/union")
@Api(value = "union", tags = "银联")
@Slf4j
public class UnionController extends BaseController {

    @Autowired
    private UnionService unionService;

    @Autowired
    private ShopInfoService shopInfoService;


    @PostMapping("/upload")
    @ApiModelProperty(value = "银联文件上传")
    public R upload(@Validated @RequestBody UnionPicUpload unionPicUpload) {
        Long userId = getUserId();
        String requestSeq = unionService.createSeq(userId);
        unionPicUpload.setRequestSeq(requestSeq);
        unionPicUpload.setCreateStaff(String.valueOf(userId));
        unionPicUpload.setShopId(shopInfoService.getOne(Wrappers.<ShopInfo>lambdaQuery().eq(ShopInfo::getCreateStaff, userId).ne(ShopInfo::getStatusCd, ShopConstants.SHOP_DELETE)).getShopId());
        return unionService.upload(unionPicUpload);
    }

    @PostMapping("/autoReg")
    @ApiModelProperty(value = "企业商户入网")
    public R autoReg(@Validated @RequestBody Union union) {
        Long userId = getUserId();
        String requestSeq = unionService.createSeq(userId);
        union.setRequestSeq(requestSeq);
        union.setCreateStaff(String.valueOf(userId));
//        union.setShopId(shopInfoService.getOne(Wrappers.<ShopInfo>lambdaQuery().eq(ShopInfo::getCreateStaff, userId)).getShopId());
        return unionService.autoReg(union);
    }

    @PostMapping("/requestAccountVerify")
    @ApiModelProperty(value = "发起对公账户验证交易")
    public R requestAccountVerify() {
        Long userId = getUserId();
        return unionService.requestAccountVerify(userId);
    }

    @PostMapping("/companyAccountVerify")
    @ApiModelProperty(value = "对公账户认证接口")
    public R companyAccountVerify(@RequestBody String transAmt) {
        Long userId = getUserId();
        return unionService.companyAccountVerify(userId,transAmt);
    }

    @PostMapping("/complexAgreementSign")
    @ApiModelProperty(value = "组装前台签约地址接口")
    public R complexAgreementSign() {
        Long userId = getUserId();
        return unionService.complexAgreementSign(userId);
    }
    @PostMapping("/applyQry")
    @ApiModelProperty(value = "入网状态查询")
    public R complexApplyQry() {
        Long userId = getUserId();
        Union union= unionService.getOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, userId));
        return R.ok(unionService.complexApplyQry(union));
    }
    @PostMapping("/terminalsQry")
    @ApiModelProperty(value = "商户终端号查询")
    public R terminalsQry() {
        Long userId = getUserId();
        Union union= unionService.getOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, userId));
        return R.ok(unionService.terminalsQry(union));
    }

    @GetMapping("/unionMcc")
    @ApiModelProperty(value = "银联MCC查询")
    public R getUnionMccList() {
        return unionService.getUnionMccList();
    }

    @GetMapping("/unionRegion")
    @ApiModelProperty(value = "银联地址查询")
    public R getUnionRegionList(String parCode) {
        return unionService.getUnionRegionList(parCode);
    }

    @GetMapping
    @ApiModelProperty(value = "银联信息查询")
    public R getUnionInfo() {
        Long userId = getUserId();
        Union one = unionService.getOne(Wrappers.<Union>lambdaQuery().eq(Union::getCreateStaff, userId));
        if (ObjectUtil.isNotEmpty(one)){
            one.setLegalName(DataDesensitizationUtil.replaceLeft(one.getLegalName(),1));
            one.setLegalIdcardNo(DataDesensitizationUtil.replaceLeftRight(one.getLegalIdcardNo(),6,4));
            one.setLegalMobile(DataDesensitizationUtil.replaceLeftRight(one.getLegalMobile(),3,4));
            one.setBankNo(DataDesensitizationUtil.replaceLeftRight(one.getBankNo(),3,3));
            one.setBankAcctNo(DataDesensitizationUtil.replaceLeftRight(one.getBankAcctNo(),3,3));
            one.setLegalmanHomeAddr(DataDesensitizationUtil.replaceLeft(one.getLegalmanHomeAddr(),6));
            if (!StringUtil.isEmpty(one.getShareholderName())){
                one.setShareholderName(DataDesensitizationUtil.replaceLeft(one.getShareholderName(),1));
                one.setShareholderCertno(DataDesensitizationUtil.replaceLeftRight(one.getShareholderCertno(),4,4));
                one.setShareholderHomeAddr(DataDesensitizationUtil.replaceLeft(one.getShareholderHomeAddr(),6));
            }
        }
        return R.ok(one);
    }

    @GetMapping("/unionInfoEcho")
    @ApiModelProperty(value = "银联信息回显")
    public R getUnionInfoEcho() {
        Long userId = getUserId();
        Union one = unionService.getUnionInfoEcho(userId);
        return R.ok(one);
    }


    @GetMapping("/unionFileEcho")
    @ApiModelProperty(value = "银联图片信息回显")
    public R getUnionFileEcho(String filePath) {
        return unionService.getUnionFileEcho(filePath);
    }
    @PostMapping("/toUnion")
    @Anonymous
    public String toUnion(@RequestBody ChinaumsMerchantComplexUploadDto dto) {
        unionService.toUnion(dto);
        return null;
    }
    @PostMapping("/createAgreement")
    @Anonymous
    public String createAgreement() {
        List<String> list = Lists.newLinkedList();
        list.add("自助签约有限责任公司");
        list.add("汪自助");
        return ImageUtil.word2Image("https://trade.sccdex.com/works-sample/880ba30a-ddd4-48e8-921b-1eb5213266df.png", 1, "宋体", Font.PLAIN, 35, Color.BLACK, list, 195, 768, "png");
    }
    @GetMapping("/test")
    @Anonymous
    public String resultJsonAnalysis() {
        String reqBody = "{\"mer_no\": \"898510108677515\",\"res_msg\": \"请求成功\",\"mapp_info_list\": [{\"apptype_id\": \"9\",\"submchnt_info_list\": [{\"submchntBriefname\": \"课标科技\",\"term_app_no_list\": \"6LEMVPAK\",\"submchntId\": \"46163ad8b2db43ebb13486ba12d27ef2\",\"submchntName\": \"课标科技\"}],\"qrcodeQmfUrl\": \"https://qr.chinaums.com/bills/qrCode.do?id=10092504019821609107988999\",\"mapp_no\": \"898510108677515\",\"aliAuthorized\": \"UNAUTHORIZED\",\"term_app_no_list\": \"6LEMVPAK\",\"wxAuthorized\": \"AUTHORIZE_STATE_UNAUTHORIZED\",\"card_type_fee_list\": [{\"card_fee\": \"0.45\",\"card_type\": \"借记卡费率\"}, {\"card_fee\": \"0.6\",\"card_type\": \"贷记卡费率\"}, {\"card_fee\": \"0.3\",\"card_type\": \"支付宝\"}, {\"card_fee\": \"0.3\",\"card_type\": \"微信钱包\"}, {\"card_fee\": \"0.45\",\"card_type\": \"云闪付-借记\"}, {\"card_fee\": \"0.6\",\"card_type\": \"云闪付-贷记\"}, {\"card_fee\": \"0\",\"card_type\": \"企业网关\"}],\"qrcodeYlUrl\": \"https://qr.95516.com/48020000/10092504019821609107988999\"}, {\"apptype_id\": \"74\",\"submchnt_info_list\": [{\"submchntBriefname\": \"课标科技\",\"term_app_no_list\": \"3SNSZBQQ\",\"submchntId\": \"46163ad8b2db43ebb13486ba12d27ef2\",\"submchntName\": \"课标科技\"}],\"mapp_no\": \"898510108677514\",\"term_app_no_list\": \"3SNSZBQQ\",\"card_type_fee_list\": [{\"card_fee\": \"0\",\"card_type\": \"借记卡费率\"}, {\"card_fee\": \"0\",\"card_type\": \"贷记卡费率\"}]}],\"merMsRelation\": \"{\\\"8f67164137f049b0b97312fb809028fa\\\":\\\"0\\\"}\",\"mchnt_id\": \"2b81dd6a265d43c4b0c75bf44fe3944a\",\"aliPayRecordMchntNo\": \"2088070790384925\",\"apply_status\": \"03\",\"unionPayRecordMchntNo\": \"898510108677515\",\"res_code\": \"0000\",\"accesser_acct\": \"254\",\"apply_status_msg\": \"入网成功\",\"ums_reg_id\": \"02503281044087103654\",\"wechatPayRecordMchntNo\": \"*********\",\"company_no\": \"\",\"fail_reason\": \"\",\"request_seq\": \"verify_1356574180845289472\",\"contract_state\": \"sign_success\",\"isPayable\": \"00\",\"mchntName\": \"课标科技\"}";
//        String response = "chinaums_merchant_complex_upload_response";
        cn.hutool.json.JSONObject result = JSONUtil.parseObj(reqBody);
        JSONArray mapp_info_list = result.getJSONArray("mapp_info_list");
        String secondMappNo = mapp_info_list.getJSONObject(1).getStr("mapp_no");
        System.out.println(secondMappNo);
        return secondMappNo;
    }
//        if (!"10000".equals(result.get("code"))) {
//            log.error("开放平台请求异常:{}", reqBody);
//            if (result.containsKey("sub_msg")) {
//                throw new RuntimeException(result.getStr("sub_msg"));
//            } else if (result.containsKey("msg")) {
//                throw new RuntimeException(result.getStr("msg"));
//            }
//            throw new RuntimeException("请求异常，请联系管理员");
//        }
//        JSONObject dataJson = result.get("data", JSONObject.class);
//        if (Objects.isNull(dataJson)) {
//            return JSON.toJSONString(reqBody);
//        }
//        Object dataTwoJson = dataJson.get("data");
//        String string = JSON.toJSONString(Objects.isNull(dataTwoJson) ? dataJson : dataTwoJson);
//        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(string.replace("\\", "").replace("\"{", "{").replace("}\"", "}"));
//        return null;


//    String s ="{\"code\": \"0\",\"data\": { \"input\": \"0xffd89b6f\",\"hash\": \"0xddf0c0399a43eb5af0d7274f02c6e591d6f63e41879e170f69dd51c3e2da14a4\"}}";
//        log.info("文创链上链返回数据：{}",s);
//        JSONObject jsonObject = JSONObject.parseObject(s);
//        JSONObject data = jsonObject.getJSONObject("data");
//        data.put("input",null);
//        jsonObject.put("data",data);
//    return jsonObject;
//    }



}
