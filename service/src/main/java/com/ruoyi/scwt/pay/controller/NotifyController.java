package com.ruoyi.scwt.pay.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alipay.v3.ApiException;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.scwt.pay.AliPayClient;
import com.ruoyi.scwt.pay.WxPayClient;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;
import com.ruoyi.scwt.pay.service.OrderService;
import com.ruoyi.scwt.pay.service.OrderSubDetailService;
import com.ruoyi.scwt.sop.config.SopProperties;
import com.ruoyi.scwt.sop.util.SignatureUtil;
import com.wechat.pay.java.core.notification.RequestParam;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;

/**
 * 实名认证
 */
@RestController
@RequestMapping("/notify")
@Api(value = "notify", tags = "支付回调")
@Slf4j
public class NotifyController extends BaseController {

    @Autowired
    private AliPayClient aliPayClient;
    @Autowired
    private WxPayClient wxPayClient;
    @Autowired
    private SopProperties sopProperties;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderSubDetailService orderSubDetailService;

    @PostMapping("/aliPay")
    @Anonymous
    public void aliPayNotify(HttpServletRequest request, HttpServletResponse response) {
        log.info("支付宝回调=============");
        log.info("回调数据{}", request.getParameterMap());
        try {
            aliPayClient.aliPayNotify(request, response);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    @PostMapping("/wxPay")
    @Anonymous
    public void wechat(HttpServletRequest request, @RequestBody String body, HttpServletResponse response) {
        try {
            // 构造 RequestParam
            com.wechat.pay.java.core.notification.RequestParam requestParam = new RequestParam.Builder()
                    // 序列号
                    .serialNumber(request.getHeader("Wechatpay-Serial"))
                    // 随机数
                    .nonce(request.getHeader("Wechatpay-Nonce"))
                    // 签名
                    .signature(request.getHeader("Wechatpay-Signature"))
                    // 时间戳
                    .timestamp(request.getHeader("Wechatpay-Timestamp"))
                    .body(body)
                    .build();
            wxPayClient.wechat(requestParam, response);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/unionContract")
    @Anonymous
    public void unionContract(HttpServletRequest request, @RequestBody JSONObject body, HttpServletResponse response) {
        try {
            log.info("银联进件回调=============");
            log.info("回调数据{}", body);
            Object heli = body.get("chinaums");
            Object sign = body.get("sign");
            // 使用平台公钥对回调信息里heli/chinaums的value值进行验签，验签成功才执行后续逻辑
            boolean flag = SignatureUtil.rsa256CheckContent(heli.toString(), sign.toString(), sopProperties.getPlatformPublicKey(), "UTF-8");
            if (flag) {
                log.info("验签结果{}", flag);
                // 根据回调信息进行商户支付回调业务处理
                // TODO 处理成功返回success即可，失败有2次重试机制
                response.getWriter().print("success");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


//@PostMapping(value = "/unionPay")
//@Anonymous
//public void callback(@org.springframework.web.bind.annotation.RequestParam("chinaums") String chinaumsStr, // 接收表单字段
//                     @org.springframework.web.bind.annotation.RequestParam("sign") String sign,
//    HttpServletResponse response) {
//
//    try {
//        log.info("银联支付回调参数 chinaums: {}", chinaumsStr);
//        log.info("银联支付回调参数 sign: {}", sign);
//
//        // 将chinaums字符串转为JSONObject
//        JSONObject chinaumsJson = JSONUtil.parseObj(chinaumsStr);
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("chinaums", chinaumsJson);
//        jsonObject.put("sign", sign);
//
//        // 后续处理逻辑保持不变...
//    } catch (Exception e) {
//        // 异常处理...
//    }
//}

    @PostMapping(value = "/unionPay")
    @Anonymous
    public void callback(@RequestBody JSONObject jsonObject,HttpServletResponse response) {
//        JSONObject jsonObject =new JSONObject("{\"chinaums\":\"{\\n    \\\"billPayment\\\": \\\"{\\\\\\\"buyerUsername\\\\\\\":\\\\\\\"182****2826\\\\\\\",\\\\\\\"payTime\\\\\\\":\\\\\\\"2024-12-31 15:53:48\\\\\\\",\\\\\\\"buyerCashPayAmt\\\\\\\":100,\\\\\\\"connectSys\\\\\\\":\\\\\\\"UNIONPAY\\\\\\\",\\\\\\\"paySeqId\\\\\\\":\\\\\\\"46763301081N\\\\\\\",\\\\\\\"invoiceAmount\\\\\\\":100,\\\\\\\"settleDate\\\\\\\":\\\\\\\"2024-12-31\\\\\\\",\\\\\\\"buyerId\\\\\\\":\\\\\\\"2088802801827048\\\\\\\",\\\\\\\"receiptAmount\\\\\\\":100,\\\\\\\"totalAmount\\\\\\\":100,\\\\\\\"couponAmount\\\\\\\":0,\\\\\\\"billBizType\\\\\\\":\\\\\\\"bills\\\\\\\",\\\\\\\"buyerPayAmount\\\\\\\":100,\\\\\\\"targetOrderId\\\\\\\":\\\\\\\"2024123122001427041442998117\\\\\\\",\\\\\\\"payDetail\\\\\\\":\\\\\\\"???????1.00??\\\\\\\",\\\\\\\"merOrderId\\\\\\\":\\\\\\\"31Q77UA101202412319AC0\\\\\\\",\\\\\\\"status\\\\\\\":\\\\\\\"TRADE_SUCCESS\\\\\\\",\\\\\\\"targetSys\\\\\\\":\\\\\\\"Alipay 2.0\\\\\\\"}\\\",\\n    \\\"qrCodeId\\\": \\\"31Q72412310305331156480515\\\",\\n    \\\"billDesc\\\": \\\"????????????\\\",\\n    \\\"sign\\\": \\\"EB44B7FD1BA91F30F617FB86BD4DBF88A87AAE3F74A2F9BD40893571FEB0DA0B\\\",\\n    \\\"merName\\\": \\\"????????????\\\",\\n    \\\"mid\\\": \\\"898510159717017\\\",\\n    \\\"billDate\\\": \\\"2024-12-31\\\",\\n    \\\"qrCodeType\\\": \\\"BILLPAY\\\",\\n    \\\"mchntUuid\\\": \\\"2d9081bc7fe01c0f017fe427de8b5d89\\\",\\n    \\\"tid\\\": \\\"50485696\\\",\\n    \\\"instMid\\\": \\\"QRPAYDEFAULT\\\",\\n    \\\"receiptAmount\\\": \\\"100\\\",\\n    \\\"totalAmount\\\": \\\"100\\\",\\n    \\\"oA\\\": \\\"iybL\\\",\\n    \\\"createTime\\\": \\\"2024-12-31 15:53:31\\\",\\n    \\\"billStatus\\\": \\\"PAID\\\",\\n    \\\"cardAttr\\\": \\\"BALANCE\\\",\\n    \\\"signType\\\": \\\"SHA256\\\",\\n    \\\"notifyId\\\": \\\"0302cb8d-0e01-40ea-8490-f8427022fb63\\\",\\n    \\\"billNo\\\": \\\"31Q77UA101202412319AC\\\",\\n    \\\"subInst\\\": \\\"102200\\\",\\n    \\\"seqId\\\": \\\"46763301081N\\\",\\n    \\\"billQRCode\\\": \\\"https://qr.95516.com/48020000/31Q72412310305331156480515\\\"\\n}\",\"sign\":\"GalaYtHI69/wJNXaPZVZremOVvDkw2mGKXbtMVEU3vnJgOGOYOTk0yTMUehaLQFOZ63Se5SMQspWlrtVBvdB8fPulOM1pt8JYvRODjTlZCB3UsYx0vlBNSTE3s5DRzJFUIhUH7CFuoQlVcc88lsDovck9k/QAblYIz9nP0jqovR8xW3EVlGy9ZaMASJkLkcIm60NzyTY+xpIzHzmjt9Ceb2B3bKNCH0lWT7dYAl7JPrbFM1ZVY0g3z7x4yIwqxH1Mha7C7/J+ijUPzpenDAqTUVCKQDwhlJrmRZPCfwUUbm/iPBdT0lRtvayCtYHLKBBFSSVSRcRO2P5LtmOJpoJ0A==\"}");
        try {
            log.info("银联支付回调=============");
            log.info("回调数据{}", jsonObject);
            Object chinaums = jsonObject.get("chinaums");
            Object sign = jsonObject.get("sign");
            // 使用平台公钥对回调信息里heli/chinaums的value值进行验签，验签成功才执行后续逻辑
//            String replace = chinaums.toString().replace("\n", "").replace("\\\"", "\"").replace("\\\\\\\"", "\"");
            boolean flag = SignatureUtil.rsa256CheckContent(chinaums.toString(), sign.toString(), sopProperties.getPlatformPublicKey(), "UTF-8");
            if (flag) {
                log.info("验签结果{}", flag);
                // 根据回调信息进行商户支付回调业务处理
                if (jsonObject.getJSONObject("chinaums").getJSONObject("billPayment").getStr("status").equals("TRADE_SUCCESS")){
                    String orderNo = jsonObject.getJSONObject("chinaums").getStr("billNo");
                    OrderSubDetail orderSubDetail = orderSubDetailService.getOne(Wrappers.<OrderSubDetail>lambdaQuery().eq(OrderSubDetail::getSopOrderNo, orderNo));
                    orderService.success(orderSubDetail);
                }
                response.getWriter().print("success");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
