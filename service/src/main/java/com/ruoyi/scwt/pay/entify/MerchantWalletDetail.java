package com.ruoyi.scwt.pay.entify;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 合利宝钱包明细
 * <AUTHOR>
 * @date 2022-06-22 11:05
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("merchant_wallet_detail")
@ApiModel(value="MerchantWalletDetail对象")
public class MerchantWalletDetail extends Model<MerchantWalletDetail> {
    private static final long serialVersionUID = 1L;
    /**
     * 编号
     */
    @TableId
    @ApiModelProperty(value = "编号")
    private Long Id;
    /**
     * 商户编号
     */
    @ApiModelProperty(value = "商户编号")
    private String merchantNo;
    /**
     * 流水号
     */
    @ApiModelProperty(value = "流水号")
    private String recordNo;
    /**
     * 记录类型(pay：支出，income：收入，withdraw：提现)
     */
    @ApiModelProperty(value = "记录类型(pay：支出，income：收入，withdraw：提现)")
    private String recordType;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String recordRemark;
    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal money;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String statusCd;
    /**
     * 可提现金额返回数据
     */
    @ApiModelProperty(value = "可提现金额返回数据")
    private String qurWithdrawBalanceData;
    /**
     * 提现返回数据
     */
    @ApiModelProperty(value = "提现返回数据")
    private String processWithdrawData;
    /**
     * 提现状态查询数据
     */
    @ApiModelProperty(value = "提现状态查询数据")
    private String qurStatusData;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}