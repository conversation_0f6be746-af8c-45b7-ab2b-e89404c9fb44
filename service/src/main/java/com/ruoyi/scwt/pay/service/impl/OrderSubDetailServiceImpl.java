package com.ruoyi.scwt.pay.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.scwt.asset.entity.Asset;
import com.ruoyi.scwt.pay.AliPayClient;
import com.ruoyi.scwt.pay.WxPayClient;
import com.ruoyi.scwt.pay.constant.OrderConstants;
import com.ruoyi.scwt.pay.entify.Order;
import com.ruoyi.scwt.pay.entify.OrderSubDetail;
import com.ruoyi.scwt.pay.mapper.OrderMapper;
import com.ruoyi.scwt.pay.mapper.OrderSubDetailMapper;
import com.ruoyi.scwt.pay.service.OrderService;
import com.ruoyi.scwt.pay.service.OrderSubDetailService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
@RequiredArgsConstructor
public class OrderSubDetailServiceImpl extends ServiceImpl<OrderSubDetailMapper, OrderSubDetail> implements OrderSubDetailService {

    @Autowired
    private AliPayClient aliPayClient;
    @Autowired
    private WxPayClient wxPayClient;
    @Autowired
    private OrderService orderService;

    @Override
    public Object pendingPaymentOrder(OrderSubDetail orderSubDetail) {
        switch (orderSubDetail.getPayChannel()) {
            case OrderConstants.PAY_TYPE_ALIPAY:
                return null;
            case OrderConstants.PAY_TYPE_WECHAT:
                String data = wxPayClient.selectOrder(orderSubDetail.getOrderNo() + "_" + orderSubDetail.getSubOrderId());
                JSONObject result = JSONUtil.parseObj(data);
                if (result.getStr("trade_state").equals("SUCCESS")) {
                    orderService.success(orderSubDetail);
                }
                break;
            case OrderConstants.PAY_TYPE_UNIONPAY:
                return AjaxResult.error("暂不支持银联支付");
            default:
                return AjaxResult.error("支付渠道错误");
        }
        return null;
    }

    @Override
    public String thirtyDealNum(Long id) {
        // 获取当前日期
        LocalDate currentDate = LocalDate.now();
        // 获取当前日期前30天的日期
        LocalDate dateBefore30Days = currentDate.minusDays(30);

        // 格式化日期（可选）
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = dateBefore30Days.format(formatter);

        // 使用formattedDate进行后续操作
        System.out.println("前30天的日期: " + formattedDate);

        Long count = baseMapper.selectCount(Wrappers.<OrderSubDetail>lambdaQuery()
                .ge(OrderSubDetail::getCreateDate, formattedDate)
                .and(i -> i.eq(OrderSubDetail::getStatusCd, OrderConstants.ORDER_STATUS_PAID).or().eq(OrderSubDetail::getStatusCd, OrderConstants.ORDER_STATUS_SUCCESS))
                .eq(OrderSubDetail::getAssetId, id));
        return String.valueOf(count);
    }

    @Override
    public String getSalesRevenue(List<Long> assetIds, String day) {
        List<OrderSubDetail> orderSubDetails = baseMapper.selectList(Wrappers.<OrderSubDetail>lambdaQuery()
                .in(OrderSubDetail::getAssetId, assetIds)
                .eq(OrderSubDetail::getStatusCd, OrderConstants.ORDER_STATUS_PAID)
                .eq(StringUtils.isNotEmpty(day), OrderSubDetail::getCreateDate, day));
        if (orderSubDetails != null) {
            return String.valueOf(orderSubDetails.stream().map(OrderSubDetail::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        return "0";
    }

    @Override
    public Map<String, String> getSevenSalesRevenue(List<Long> assetIds) {
        Map<String, String> map = new HashMap<>();
        //获取七天前的日期
        for (int i = 0; i < 7; i++) {
            LocalDate day = LocalDate.now().minusDays(i);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String formattedDate = day.format(formatter);
            String salesRevenue = getSalesRevenue(assetIds, formattedDate);
            map.put(formattedDate, salesRevenue);
        }
        return map;
    }

    @Override
    public int updateDigOrder(String out_trade_no) {
        return baseMapper.updateDigOrder(out_trade_no);
    }
}
