//package com.ruoyi.scwt.pay.task;
//
//
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.ruoyi.scwt.pay.constant.OrderConstants;
//import com.ruoyi.scwt.pay.entify.MerchantWalletDetail;
//import com.ruoyi.scwt.pay.entify.OrderSubDetail;
//import com.ruoyi.scwt.pay.service.MerchantWalletDetailService;
//import com.ruoyi.scwt.pay.service.OrderService;
//import com.ruoyi.scwt.pay.service.OrderSubDetailService;
//import com.ruoyi.sop.dto.AgentPay;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.scheduling.annotation.Async;
//import org.springframework.stereotype.Component;
//
//import java.time.DayOfWeek;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//import java.time.temporal.ChronoUnit;
//import java.util.List;
//
//@Slf4j
//@Component
//@RequiredArgsConstructor
//@Async
//public class OrderTask {
//
//    @Autowired
//    private OrderSubDetailService orderSubDetailService;
//    @Autowired
//    private MerchantWalletDetailService merchantWalletDetailService;
//    @Autowired
//    private OrderService orderService;
//
//    //    @Scheduled(fixedDelay  = 1000 * 60 * 30) //每隔30分钟执行一次
//    public void pendingPaymentOrder() {
//        log.info("======轮询查询待支付订单定时任务开启======");
//        List<OrderSubDetail> orderSubDetailList = orderSubDetailService.list(Wrappers.<OrderSubDetail>lambdaQuery().eq(OrderSubDetail::getStatusCd, OrderConstants.ORDER_STATUS_UNPAID));
//        log.info("待支付订单数为：{}条", orderSubDetailList.size());
//        for (OrderSubDetail orderSubDetail : orderSubDetailList) {
//            orderSubDetailService.pendingPaymentOrder(orderSubDetail);
//        }
//    }
//
////        @Scheduled(cron = "0 0 10 ? * MON-FRI")  // 每个工作日上午10点执行
////    @Scheduled(fixedDelay = 1000 * 60 * 30) //每隔30分钟执行一次
//    public void processWithdraw() {
//        log.info("======提现定时任务开启======");
//        //获取当前时间前7个工作日的日期
//
//        LocalDate targetDate = getPreviousWorkday(7);
//        LocalDateTime startTime = targetDate.atStartOfDay(); // 当天 00:00:00
//        LocalDateTime endTime = targetDate.atTime(23, 59, 59); // 当天 23:59:59
//        log.info("当前时间前7个工作日的日期是：{}", targetDate);
//
//        List<OrderSubDetail> orderSubDetailList = orderSubDetailService.list(Wrappers.<OrderSubDetail>lambdaQuery()
//                .eq(OrderSubDetail::getStatusCd, OrderConstants.ORDER_STATUS_PAID)
//                .like(OrderSubDetail::getPayChannel, "union")
//                .between(OrderSubDetail::getStatusDate, startTime, endTime));
//        log.info("提现订单数为：{}条", orderSubDetailList.size());
//        orderSubDetailList.forEach(orderSubDetail -> {
//            MerchantWalletDetail merchantWalletDetail = merchantWalletDetailService.getOne(Wrappers.<MerchantWalletDetail>lambdaQuery().eq(MerchantWalletDetail::getRecordNo, orderSubDetail.getOrderNo()));
//            if (merchantWalletDetail == null) {
//                AgentPay agentPay =new AgentPay();
//                agentPay.setOrderNo(orderSubDetail.getOrderNo());
//                orderService.processWithdraw(agentPay);
//            }
//        });
//    }
//
////    @Scheduled(fixedDelay = 30 * 60 * 1000) // 每次执行完成后30分钟再次执行
////    @Scheduled(fixedDelay = 1000 * 60 * 30) //每隔30分钟执行一次
//    public void qurProcessWithdraw() {
//        // 时间窗口校验
//        LocalDateTime now = LocalDateTime.now();
//        DayOfWeek dayOfWeek = now.getDayOfWeek();
//        int hour = now.getHour();
//
//        // 只在工作日 11:00-18:00 执行
//        if (dayOfWeek == DayOfWeek.SATURDAY
//                || dayOfWeek == DayOfWeek.SUNDAY
//                || hour < 11
//                || hour >= 18) {
//            log.info("非执行时段，跳过本次任务");
//            return;
//        }
//        log.info("======提现状态定时任务开启======");
//        List<MerchantWalletDetail> list = merchantWalletDetailService.list(Wrappers.<MerchantWalletDetail>lambdaQuery()
//                .eq(MerchantWalletDetail::getStatusCd, OrderConstants.WITHDRAW_STATUS_INIT));
//        log.info("查询提现状态数为：{}条", list.size());
//        list.forEach(merchantWalletDetail -> {
//            AgentPay agentPay =new AgentPay();
//            agentPay.setOrderNo(merchantWalletDetail.getRecordNo());
//            orderService.qurProcessWithdraw(agentPay);
//        });
//    }
//
//    /**
//     * 获取当前时间前N个工作日的日期
//     *
//     * @param workdays 需要回溯的工作日天数
//     * @return LocalDate 目标日期
//     */
//    public static LocalDate getPreviousWorkday(int workdays) {
//        LocalDate date = LocalDate.now();
//        int count = 0;
//
//        while (count < workdays) {
//            date = date.minus(1, ChronoUnit.DAYS);
//            // 跳过周末
//            if (date.getDayOfWeek() != DayOfWeek.SATURDAY
//                    && date.getDayOfWeek() != DayOfWeek.SUNDAY) {
//                count++;
//            }
//        }
//        return date;
//    }
//
//
//}
