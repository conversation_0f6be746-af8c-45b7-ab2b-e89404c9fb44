package com.ruoyi.scwt.zone.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.scwt.zone.entity.ZoneHome;
import com.ruoyi.scwt.zone.entity.ZoneLabelRel;
import com.ruoyi.scwt.zone.mapper.ZoneHomeMapper;
import com.ruoyi.scwt.zone.mapper.ZoneLabelRelMapper;
import com.ruoyi.scwt.zone.service.ZoneHomeService;
import com.ruoyi.scwt.zone.service.ZoneLabelRelService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Service
public class ZoneLabelRelServiceImpl extends ServiceImpl<ZoneLabelRelMapper, ZoneLabelRel> implements ZoneLabelRelService {

    @Override
    public void insertLabelRel(Long zoneId, String labelIds) {
        String[] split = labelIds.split(",");
        for (String labelId : split) {
            ZoneLabelRel zoneLabelRel = new ZoneLabelRel();
            zoneLabelRel.setZoneId(zoneId);
            zoneLabelRel.setLabelId(Long.valueOf(labelId));
            this.save(zoneLabelRel);
        }
    }
}
