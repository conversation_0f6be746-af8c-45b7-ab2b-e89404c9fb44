package com.ruoyi.issue.issuer.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.issue.issuer.domain.DigAssetIssuer;
import com.ruoyi.issue.issuer.domain.DigAssetIssuerRel;

/**
 * 资产发行方Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface DigAssetIssuerService extends IService<DigAssetIssuer>
{
    /**
     * 查询资产发行方
     * 
     * @param issuerId 资产发行方主键
     * @return 资产发行方
     */
    public DigAssetIssuer selectDigAssetIssuerByIssuerId(Long issuerId);

    /**
     * 查询资产发行方列表
     * 
     * @param digAssetIssuer 资产发行方
     * @return 资产发行方集合
     */
    public List<DigAssetIssuer> selectDigAssetIssuerList(DigAssetIssuer digAssetIssuer, List<SysRole>  roles, Long userId);

    /**
     * 新增资产发行方
     * 
     * @param digAssetIssuer 资产发行方
     * @return 结果
     */
    public int insertDigAssetIssuer(DigAssetIssuer digAssetIssuer);

    /**
     * 修改资产发行方
     * 
     * @param digAssetIssuer 资产发行方
     * @return 结果
     */
    public int updateDigAssetIssuer(DigAssetIssuer digAssetIssuer);

    /**
     * 批量删除资产发行方
     * 
     * @param issuerIds 需要删除的资产发行方主键集合
     * @return 结果
     */
    public int deleteDigAssetIssuerByIssuerIds(Long[] issuerIds);

    /**
     * 删除资产发行方信息
     * 
     * @param issuerId 资产发行方主键
     * @return 结果
     */
    public int deleteDigAssetIssuerByIssuerId(Long issuerId);

    /**
     * 获取资产发行方名称列表
     * @param issuerName
     * @return
     */
    List<DigAssetIssuer> getIssuerNameList(String issuerName,List<SysRole>  roles,Long userId);

    /**
     * 插入资产发行方关系
     * @param issuerRel
     */
    void insertRel(DigAssetIssuerRel issuerRel);

    /**
     * 根据资产ID获取资产发行方名称列表
     * @param assetId
     * @return
     */
    List<String> selectIssuerNamesByAssetId(Long assetId);

    List<DigAssetIssuer> selectIssuerListByAssetId(Long assetId);

    void removeRel(Long assetId);
}
