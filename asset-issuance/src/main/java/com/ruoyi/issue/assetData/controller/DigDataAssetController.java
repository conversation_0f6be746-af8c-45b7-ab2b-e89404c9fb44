package com.ruoyi.issue.assetData.controller;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.issue.asset.service.DigDigitalAssetService;
import com.ruoyi.issue.assetData.vo.DigDataAssetVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.assetData.domain.DigDataAsset;
import com.ruoyi.issue.assetData.service.IDigDataAssetService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 用户数据资产Controller
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
@RestController
@RequestMapping("/assetData/assetData")
@Api(value = "用户数据资产",tags = "用户数据资产管理")
public class DigDataAssetController extends BaseController
{
    @Autowired
    private IDigDataAssetService digDataAssetService;

    @Autowired
    private DigDigitalAssetService digDigitalAssetService;

    /**
     * 查询用户数据资产列表
     */
    @PreAuthorize("@ss.hasPermi('assetData:assetData:list')")
    @GetMapping("/list")
    @ApiOperation("用户数据资产列表")
    public TableDataInfo list(DigDataAsset digDataAsset)
    {
        startPage();
        List<DigDataAsset> list = digDataAssetService.selectDigDataAssetList(digDataAsset);
        return getDataTable(list);
    }

    /**
     * 导出用户数据资产列表
     */
    @PreAuthorize("@ss.hasPermi('assetData:assetData:export')")
    @Log(title = "导出用户数据资产列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出用户数据资产列表")
    public void export(HttpServletResponse response, DigDataAsset digDataAsset)
    {
        List<DigDataAsset> list = digDataAssetService.selectDigDataAssetList(digDataAsset);
        ExcelUtil<DigDataAsset> util = new ExcelUtil<DigDataAsset>(DigDataAsset.class);
        util.exportExcel(response, list, "用户数据资产数据");
    }

    /**
     * 获取用户数据资产详细信息
     */
    @PreAuthorize("@ss.hasPermi('assetData:assetData:query')")
    @GetMapping(value = "/{dataAssetId}")
    @ApiOperation("用户数据资产详细信息")
    public AjaxResult getInfo(@PathVariable("dataAssetId") Long dataAssetId)
    {
        return success(digDataAssetService.selectDigDataAssetByDataAssetId(dataAssetId));
    }

    /**
     * 新增用户数据资产
     */
    @PreAuthorize("@ss.hasPermi('assetData:assetData:add')")
    @Log(title = "新增用户数据资产", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增用户数据资产")
    public AjaxResult add(@RequestBody DigDataAsset digDataAsset)
    {
        return toAjax(digDataAssetService.insertDigDataAsset(digDataAsset));
    }

    /**
     * 修改用户数据资产
     */
    @PreAuthorize("@ss.hasPermi('assetData:assetData:edit')")
    @Log(title = "用户数据资产", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改用户数据资产")
    public AjaxResult edit(@RequestBody DigDataAsset digDataAsset)
    {
        return toAjax(digDataAssetService.updateDigDataAsset(digDataAsset));
    }

    /**
     * 删除用户数据资产
     */
    @PreAuthorize("@ss.hasPermi('assetData:assetData:remove')")
    @Log(title = "用户数据资产", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dataAssetIds}")
    @ApiOperation("删除用户数据资产")
    public AjaxResult remove(@PathVariable Long[] dataAssetIds)
    {
        return toAjax(digDataAssetService.deleteDigDataAssetByDataAssetIds(dataAssetIds));
    }

    /**------------------------------------------------客户端--------------------------------------------------*/

    @GetMapping("/client/list")
    @ApiOperation("用户数据资产列表")
    public TableDataInfo clientList(DigDataAsset digDataAsset)
    {
        startPage();
        digDataAsset.setUserId(getUserId());
        List<DigDataAsset> list = digDataAssetService.selectDigDataAssetList(digDataAsset);
        List<DigDataAssetVo> listVo = new ArrayList<>();
        for (DigDataAsset dataAsset : list) {
            DigDataAssetVo vo = new DigDataAssetVo();
            BeanUtil.copyProperties(dataAsset, vo);
//            vo.setAssetCover(digDigitalAssetService.getCoverById(dataAsset.getAssetId()));
            vo.setAssetCoverThumbnail(digDigitalAssetService.getCoverById(dataAsset.getAssetId()));
            listVo.add(vo);
        }
        return getDataTable(listVo);
    }

    @GetMapping("/client/getInfo/{dataAssetId}")
    @ApiOperation("用户数据资产详细信息")
    public AjaxResult clientGetInfo(@PathVariable("dataAssetId") Long dataAssetId)
        {
        DigDataAssetVo vo = new DigDataAssetVo();
        DigDataAsset dataAsset = digDataAssetService.selectDigDataAssetByDataAssetId(dataAssetId);
        BeanUtil.copyProperties(dataAsset, vo);
        vo.setAssetCoverThumbnail(digDigitalAssetService.getCoverById(dataAsset.getAssetId()));
        return success(vo);
    }
}
