package com.ruoyi.issue.assetData.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.assetData.domain.DigDataAsset;

/**
 * 用户数据资产Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-04
 */
public interface DigDataAssetMapper extends BaseMapper<DigDataAsset>
{
    /**
     * 查询用户数据资产
     * 
     * @param dataAssetId 用户数据资产主键
     * @return 用户数据资产
     */
    public DigDataAsset selectDigDataAssetByDataAssetId(Long dataAssetId);

    /**
     * 查询用户数据资产列表
     * 
     * @param digDataAsset 用户数据资产
     * @return 用户数据资产集合
     */
    public List<DigDataAsset> selectDigDataAssetList(DigDataAsset digDataAsset);

    /**
     * 新增用户数据资产
     * 
     * @param digDataAsset 用户数据资产
     * @return 结果
     */
    public int insertDigDataAsset(DigDataAsset digDataAsset);

    /**
     * 修改用户数据资产
     * 
     * @param digDataAsset 用户数据资产
     * @return 结果
     */
    public int updateDigDataAsset(DigDataAsset digDataAsset);

    /**
     * 删除用户数据资产
     * 
     * @param dataAssetId 用户数据资产主键
     * @return 结果
     */
    public int deleteDigDataAssetByDataAssetId(Long dataAssetId);

    /**
     * 批量删除用户数据资产
     * 
     * @param dataAssetIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigDataAssetByDataAssetIds(Long[] dataAssetIds);
}
