package com.ruoyi.issue.pay.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.pay.domain.DigAssetOrder;

/**
 * 数字资产订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
public interface DigAssetOrderMapper extends BaseMapper<DigAssetOrder>
{
    /**
     * 查询数字资产订单
     * 
     * @param orderId 数字资产订单主键
     * @return 数字资产订单
     */
    public DigAssetOrder selectDigAssetOrderByOrderId(Long orderId);

    /**
     * 查询数字资产订单列表
     * 
     * @param digAssetOrder 数字资产订单
     * @return 数字资产订单集合
     */
    public List<DigAssetOrder> selectDigAssetOrderList(DigAssetOrder digAssetOrder);

    /**
     * 新增数字资产订单
     * 
     * @param digAssetOrder 数字资产订单
     * @return 结果
     */
    public int insertDigAssetOrder(DigAssetOrder digAssetOrder);

    /**
     * 修改数字资产订单
     * 
     * @param digAssetOrder 数字资产订单
     * @return 结果
     */
    public int updateDigAssetOrder(DigAssetOrder digAssetOrder);

    /**
     * 删除数字资产订单
     * 
     * @param orderId 数字资产订单主键
     * @return 结果
     */
    public int deleteDigAssetOrderByOrderId(Long orderId);

    /**
     * 批量删除数字资产订单
     * 
     * @param orderIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigAssetOrderByOrderIds(Long[] orderIds);
}
