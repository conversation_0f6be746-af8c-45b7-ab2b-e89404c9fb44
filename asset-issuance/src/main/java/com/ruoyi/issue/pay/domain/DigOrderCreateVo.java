package com.ruoyi.issue.pay.domain;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "订单创建VO对象")
public class DigOrderCreateVo {

    @Excel(name = "买方ID")
    @ApiModelProperty(value = "买方ID")
    private Long buyerId;

    /** 购买数量 */
    @Excel(name = "购买数量")
    @ApiModelProperty(value = "购买数量")
    private Long buyQuantity;

    /** 交易类型 */
    @Excel(name = "交易类型")
    @ApiModelProperty(value = "交易类型")
    private String tradeType;

    /** 支付渠道 */
    @Excel(name = "支付渠道")
    @ApiModelProperty(value = "支付渠道")
    private String payChannel;

    /** 资产ID */
    @Excel(name = "资产ID")
    @ApiModelProperty(value = "资产ID")
    private Long assetId;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /** 资产编号 */
    @Excel(name = "资产编号")
    @ApiModelProperty(value = "资产编号")
    private String assetCode;

    /** 错误次数 */
    @Excel(name = "错误次数")
    @ApiModelProperty(value = "错误次数")
    private Integer errorNum;

}
