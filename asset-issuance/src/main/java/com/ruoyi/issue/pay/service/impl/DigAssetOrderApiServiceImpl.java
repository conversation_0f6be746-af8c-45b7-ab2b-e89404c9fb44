package com.ruoyi.issue.pay.service.impl;

import com.ruoyi.api.issue;
import com.ruoyi.issue.pay.service.DigAssetOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DigAssetOrderApiServiceImpl implements issue.DigAssetOrderApiService {

    @Autowired
    private DigAssetOrderService digAssetOrderService;

    @Override
    public void paySuccess(String orderNo) {
        digAssetOrderService.paySuccess(orderNo);
    }
}
