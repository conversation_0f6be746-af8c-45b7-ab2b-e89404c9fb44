package com.ruoyi.issue.common.redis;

/**
 * @ClassName QueueConfiguration
 * <AUTHOR> 102306
 * @Date 2022/2/15 10:48
 */
public class QueueConfiguration {
    /**
     * 队列名称
     */
    private String queue;

    /**
     * 阻塞队列临时queue
     */
    private String tempQueue;
    /**
     * 消费者
     */
    private MsgConsumer consumer;

    private QueueConfiguration() {
    }

    public static Builder builder() {
        return new Builder();
    }

    String getQueue() {
        return queue;
    }

    MsgConsumer getConsumer() {
        return consumer;
    }

    public static class Builder {
        private final QueueConfiguration configuration = new QueueConfiguration();

        public QueueConfiguration defaultConfiguration(MsgConsumer consumer,String tempQueue) {
            configuration.consumer = consumer;
            configuration.queue = consumer.getClass().getSimpleName();
            configuration.tempQueue = tempQueue;
            return configuration;
        }

        public Builder queue(String queue) {
            configuration.queue = queue;
            return this;
        }

        public Builder consumer(MsgConsumer consumer) {
            configuration.consumer = consumer;
            return this;
        }
        public Builder tempQueue(String queue){
            configuration.tempQueue = queue;
            return this;
        }
        public QueueConfiguration build() {
            if (configuration.queue == null || configuration.queue.length() == 0) {
                if (configuration.consumer != null) {
                    configuration.queue = configuration.getClass().getSimpleName();
                }
            }
            return configuration;
        }

    }
}
