package com.ruoyi.issue.common.redis;

import cn.hutool.json.JSONUtil;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.issue.asset.service.DigAssetInventoryService;
import com.ruoyi.issue.asset.service.impl.DigAssetInventoryServiceImpl;
import com.ruoyi.issue.common.util.SpringContextHolder;
import com.ruoyi.issue.pay.domain.DigOrderCreateVo;
import com.ruoyi.issue.pay.service.impl.DigAssetOrderServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.support.TransactionTemplate;


/**
 * 创建订单异步处理线程
 *
 * @ClassName ConsumeConfiguration
 * <AUTHOR> 102306
 * @Date 2022/2/15 11:05
 */
@Slf4j
public class CreateOrderConsumer implements MsgConsumer {

    @Override
    public void onMessage(Object message) {
        log.info("接受到创建订单消息：{}", message);
        DigOrderCreateVo orderTask = JSONUtil.toBean(JSONUtil.parseObj(message), DigOrderCreateVo.class);
        DigAssetOrderServiceImpl digitalCollectionOrderService = SpringContextHolder.getBean(DigAssetOrderServiceImpl.class);
//        DigitalCollectionStockServiceImpl collectionStockService = SpringContextHolder.getBean(DigitalCollectionStockServiceImpl.class);
//        StringRedisTemplate redisTemplate = SpringContextHolder.getBean(StringRedisTemplate.class);
//        DigitalCollectionOrder order = vo.getDigitalCollectionOrder();
        //开启事务
        TransactionTemplate transactionTemplate = SpringContextHolder.getBean(TransactionTemplate.class);
        transactionTemplate.execute(status -> {
            digitalCollectionOrderService.createOrder(orderTask);
            return true;
        });
    }

    @Override
    public void onError(Object msg, Exception e) {
        log.error("创建订单队列发生错误，消息：{}", msg, e);
        DigOrderCreateVo orderTask = JSONUtil.toBean(JSONUtil.parseObj(msg), DigOrderCreateVo.class);
        orderTask.setErrorNum(orderTask.getErrorNum() == null ? 1 : orderTask.getErrorNum() + 1);
        QueueSender sender = SpringContextHolder.getBean(QueueSender.class);
        DigAssetInventoryServiceImpl digAssetInventoryService = SpringContextHolder.getBean(DigAssetInventoryServiceImpl.class);
        if (orderTask.getErrorNum() > 5) {
            log.error("订单创建队列错误次数超过5次，订单编号：{}", orderTask.getOrderNo());
            digAssetInventoryService.returnInventory(orderTask);
            sender.sendMsg(CacheConstants.ORDER_QUEUE_ERROR, orderTask);
//            LogUtil.setLog(LOG_BIZ_ORDER, LOG_LEVEL_INFO, "创建订单失败，放回订单错误队列{" + msg + "}", LOG_WRITER_PH);
        } else {
            sender.sendMsg(CacheConstants.ORDER_QUEUE, orderTask);
        }
    }
}
