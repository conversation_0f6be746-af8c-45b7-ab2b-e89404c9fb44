package com.ruoyi.issue.coupon.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.issue.common.constant.Constants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.coupon.domain.DigCoupon;
import com.ruoyi.issue.coupon.service.IDigCouponService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 权益券Controller
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@RestController
@Api(value = "权益券管理", tags = "权益券管理")
@RequestMapping("/coupon/coupon")
public class DigCouponController extends BaseController
{
    @Autowired
    private IDigCouponService digCouponService;

    /**
     * 查询权益券列表
     */
    @PreAuthorize("@ss.hasPermi('coupon:coupon:list')")
    @GetMapping("/list")
    @ApiOperation("查询权益券列表")
    public TableDataInfo list(DigCoupon digCoupon)
    {
        startPage();
        List<DigCoupon> list = digCouponService.selectDigCouponList(digCoupon);
        return getDataTable(list);
    }

    /**
     * 导出权益券列表
     */
    @PreAuthorize("@ss.hasPermi('coupon:coupon:export')")
    @Log(title = "权益券", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出权益券列表")
    public void export(HttpServletResponse response, DigCoupon digCoupon)
    {
        List<DigCoupon> list = digCouponService.selectDigCouponList(digCoupon);
        ExcelUtil<DigCoupon> util = new ExcelUtil<DigCoupon>(DigCoupon.class);
        util.exportExcel(response, list, "权益券数据");
    }

    /**
     * 获取权益券详细信息
     */
    @PreAuthorize("@ss.hasPermi('coupon:coupon:query')")
    @GetMapping(value = "/{couponId}")
    @ApiOperation("获取权益券详细信息")
    public AjaxResult getInfo(@PathVariable("couponId") Long couponId)
    {
        return success(digCouponService.selectDigCouponByCouponId(couponId));
    }

    /**
     * 新增权益券
     */
    @PreAuthorize("@ss.hasPermi('coupon:coupon:add')")
    @Log(title = "权益券", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增权益券")
    public AjaxResult add(@RequestBody DigCoupon digCoupon)
    {
        digCoupon.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        return toAjax(digCouponService.insertDigCoupon(digCoupon));
    }

    /**
     * 修改权益券
     */
    @PreAuthorize("@ss.hasPermi('coupon:coupon:edit')")
    @Log(title = "权益券", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改权益券")
    public AjaxResult edit(@RequestBody DigCoupon digCoupon)
    {
        return digCouponService.updateDigCoupon(digCoupon);
    }

    /**
     * 删除权益券
     */
    @PreAuthorize("@ss.hasPermi('coupon:coupon:remove')")
    @Log(title = "权益券", businessType = BusinessType.DELETE)
	@DeleteMapping("/{couponIds}")
    @ApiOperation("删除权益券")
    public AjaxResult remove(@PathVariable Long[] couponIds)
    {
        return toAjax(digCouponService.deleteDigCouponByCouponIds(couponIds));
    }

    /**
     * 生成权益券
     */
    @PreAuthorize("@ss.hasPermi('coupon:coupon:add')")
    @Log(title = "权益券生成", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    @ApiOperation("生成权益券")
    public AjaxResult generate(@RequestBody DigCoupon digCoupon){
        return digCouponService.generate(digCoupon);
    }

    /**
     * 获取有库存的权益券列表
     */
    @PreAuthorize("@ss.hasPermi('coupon:coupon:list')")
    @GetMapping("/listInventory")
    @ApiOperation("获取有库存的权益券列表")
    public AjaxResult listInventory(DigCoupon digCoupon){
        return success(digCouponService.listInventory(digCoupon));
    }

    /**------------------------------------------------------------客户端----------------------------------------------------*/

    /**
     * 获取用户拥有权益券数量
     */
    @GetMapping("/client/getUserCouponCount")
    @ApiOperation("获取用户拥有权益券数量")
    public AjaxResult getUserCouponCount(){
        return success(digCouponService.getUserCouponCount(getUserId()));
    }


    /**
     * 获取用户权益券列表
     */
    @GetMapping("/client/listUserCoupon")
    @ApiOperation("获取用户权益券列表")
    public AjaxResult listUserCoupon(DigCoupon digCoupon){
        return success(digCouponService.selectDigCouponListByUserId(digCoupon,getUserId()));
    }
}
