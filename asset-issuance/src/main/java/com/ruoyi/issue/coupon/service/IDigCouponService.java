package com.ruoyi.issue.coupon.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.issue.coupon.domain.DigCoupon;
import com.ruoyi.issue.coupon.domain.DigCouponCode;
import com.ruoyi.issue.coupon.vo.DigCouponCodeVo;
import com.ruoyi.issue.coupon.vo.DigCouponInventoryVo;

/**
 * 权益券Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
public interface IDigCouponService extends IService<DigCoupon>
{
    /**
     * 查询权益券
     * 
     * @param couponId 权益券主键
     * @return 权益券
     */
    public DigCoupon selectDigCouponByCouponId(Long couponId);

    /**
     * 查询权益券列表
     * 
     * @param digCoupon 权益券
     * @return 权益券集合
     */
    public List<DigCoupon> selectDigCouponList(DigCoupon digCoupon);

    /**
     * 新增权益券
     * 
     * @param digCoupon 权益券
     * @return 结果
     */
    public int insertDigCoupon(DigCoupon digCoupon);

    /**
     * 修改权益券
     * 
     * @param digCoupon 权益券
     * @return 结果
     */
    public AjaxResult updateDigCoupon(DigCoupon digCoupon);

    /**
     * 批量删除权益券
     * 
     * @param couponIds 需要删除的权益券主键集合
     * @return 结果
     */
    public int deleteDigCouponByCouponIds(Long[] couponIds);

    /**
     * 删除权益券信息
     * 
     * @param couponId 权益券主键
     * @return 结果
     */
    public int deleteDigCouponByCouponId(Long couponId);

    /**
     * 判断用户是否拥有对应的权益券，有就消耗，无则返回错误信息
     */
    public AjaxResult consumeCoupon(Long couponId, Long userId);

    AjaxResult generate(DigCoupon digCoupon);

    List<DigCouponInventoryVo> listInventory(DigCoupon digCoupon);

    /**
     * 锁定库存
     */
    public void lockInventory(Long couponId, Integer quantity,String lockType, Long lockRelId);

    /**
     * 批量解锁库存
     */
    public void unlockInventory(String lockType, Long lockRelId);
    /**
     * 查询库存列表
     */
    List<DigCouponCode> selectCouponCodeList(String lockType, Long lockRelId);

    int updateCouponCodeById(DigCouponCode couponCode);

    Long getUserCouponCount(Long userId);

    List<DigCouponCodeVo> selectDigCouponListByUserId(DigCoupon digCoupon, Long userId);

    DigCouponCodeVo getCouponInfo(String couponCode);
}
