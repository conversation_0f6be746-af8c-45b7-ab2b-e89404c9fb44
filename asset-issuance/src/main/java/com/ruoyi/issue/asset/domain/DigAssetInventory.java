package com.ruoyi.issue.asset.domain;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.baomidou.mybatisplus.annotation.Version;

/**
 * 资产库存对象 dig_asset_inventory
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Data
@ApiModel(value = "资产库存对象")
public class DigAssetInventory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 库存记录ID */
    @TableId
    @Excel(name = "库存记录ID")
    @ApiModelProperty(value = "库存记录ID")
    private Long inventoryId;

    /** 资产ID */
    @Excel(name = "资产ID")
    @ApiModelProperty(value = "资产ID")
    private Long assetId;

    /** 资产唯一编号 */
    @Excel(name = "资产唯一编号")
    @ApiModelProperty(value = "资产唯一编号")
    private String assetCode;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入库时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "入库时间")
    private LocalDateTime storageTime;

    /** 售卖类型 */
    @Excel(name = "售卖类型")
    @ApiModelProperty(value = "售卖类型")
    private String saleType;




    /** 状态代码 */
    @ApiModelProperty(value = "状态代码")
    private String statusCd;



//    /** 乐观锁版本号 */
//    @Version
//    @ApiModelProperty(value = "乐观锁版本号", hidden = true)
//    private Integer version;

    /** 备注 */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 锁定类型 */
    @Excel(name = "锁定类型")
    private String lockType;

    /** 锁定关联ID */
    @Excel(name = "锁定关联ID")
    private Long lockRelId;
}
