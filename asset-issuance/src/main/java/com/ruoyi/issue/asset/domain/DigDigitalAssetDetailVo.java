package com.ruoyi.issue.asset.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.issue.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 数字资产对象 dig_digital_asset
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@ApiModel(value = "数字资产对象")
public class DigDigitalAssetDetailVo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 资产ID，主键 */
    @TableId
    @ApiModelProperty(value = "资产ID，主键")
    private Long assetId;

    /** 资产名称 */
    @Excel(name = "资产名称")
    @ApiModelProperty(value = "资产名称")
    private String assetName;

    /** 资产封面图URL */
    @Excel(name = "资产封面图URL")
    @ApiModelProperty(value = "资产封面图URL")
    private String assetCover;

    /** 资产文件URL */
    @Excel(name = "资产文件URL")
    @ApiModelProperty(value = "资产文件URL")
    private String assetFile;

//    /** 发行方ID列表(多个以逗号隔开) */
//    @Excel(name = "发行方ID列表(多个以逗号隔开)")
//    private String issuerIds;

    /** 资产类型 */
    @Excel(name = "资产类型")
    @ApiModelProperty(value = "资产类型")
    private String assetType;

    /** 资产等级 */
    @Excel(name = "资产等级")
    @ApiModelProperty(value = "资产等级")
    private Long assetLevel;

    /** 开售时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开售时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开售时间")
    private LocalDateTime saleStartTime;

//    /** 发行数量 */
//    @Excel(name = "发行数量")
//    private Long issueQuantity;
//
//    /** 个人限购数量 */
//    @Excel(name = "个人限购数量")
//    private Long individualLimit;
//
//    /** 企业限购数量 */
//    @Excel(name = "企业限购数量")
//    private Long enterpriseLimit;
//
//    /** 空投数量 */
//    @Excel(name = "空投数量")
//    private Long airdropQuantity;
//
//    /** 活动数量 */
//    @Excel(name = "活动数量")
//    private Long activityQuantity;

    /** 发行价格 */
    @Excel(name = "发行价格")
    @ApiModelProperty(value = "发行价格")
    private BigDecimal issuePrice;
//
//    /** 所属系列ID */
//    @Excel(name = "所属系列ID")
//    private Long seriesId;

    /** 资产关键字(多个以逗号隔开) */
    @Excel(name = "资产关键字(多个以逗号隔开)")
    @ApiModelProperty(value = "资产关键字(多个以逗号隔开)")
    private String assetKeywords;

//    /** 资产简介 */
//    @Excel(name = "资产简介")
//    private String assetDesc;

    /** 资产介绍图片URL(多个以逗号隔开) */
    @Excel(name = "资产介绍图片URL(多个以逗号隔开)")
    @ApiModelProperty(value = "资产介绍图片URL(多个以逗号隔开)")
    private String introImages;

//    /** 驳回原因 */
//    @Excel(name = "驳回原因")
//    private String rejectionReason;

//    /** 状态代码 */
//    @Excel(name = "状态代码")
//    private String statusCd;
//
//    /** 状态时间 */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "状态时间", width = 30, dateFormat = "yyyy-MM-dd")
//    private LocalDateTime statusDate;
//
//    /** 创建人 */
//    @Excel(name = "创建人")
//    private String createStaff;
//
//    /** 创建时间 */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
//    private LocalDateTime createDate;
//
//    /** 修改人 */
//    @Excel(name = "修改人")
//    private String updateStaff;
//
//    /** 修改时间 */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd")
//    private LocalDateTime updateDate;
//
//    private String remark;
}
