package com.ruoyi.issue.asset.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 数字资产对象 dig_digital_asset
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Data
@ApiModel(value = "数字资产对象")
public class DigDigitalAssetAddVo extends DigDigitalAsset
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "系列ID")
    private Long  seriesId;

    @ApiModelProperty(value = "专区ID")
    private Long zoneId;

//    private String issuerIds;

}
