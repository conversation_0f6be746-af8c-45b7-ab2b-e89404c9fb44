package com.ruoyi.issue.asset.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.issue.asset.domain.DigAssetInventoryVo;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.asset.domain.DigDigitalAssetAddVo;
import com.ruoyi.issue.asset.domain.DigDigitalAssetQueryVo;

/**
 * 数字资产Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface DigDigitalAssetService extends IService<DigDigitalAsset>
{
    /**
     * 查询数字资产
     * 
     * @param assetId 数字资产主键
     * @return 数字资产
     */
    public DigDigitalAssetAddVo selectDigDigitalAssetByAssetId(Long assetId);

    /**
     * 查询数字资产列表
     * 
     * @param digDigitalAsset 数字资产
     * @return 数字资产集合
     */
    public List<DigDigitalAsset> selectDigDigitalAssetList(DigDigitalAsset digDigitalAsset, List<SysRole>  roles,Long userId);

    /**
     * 新增数字资产
     * 
     * @param digDigitalAsset 数字资产
     * @return 结果
     */
    public int insertDigDigitalAsset(DigDigitalAsset digDigitalAsset);

    /**
     * 修改数字资产
     * 
     * @param digDigitalAsset 数字资产
     * @return 结果
     */
    public int updateDigDigitalAsset(DigDigitalAsset digDigitalAsset);

    /**
     * 批量删除数字资产
     * 
     * @param assetIds 需要删除的数字资产主键集合
     * @return 结果
     */
    public int deleteDigDigitalAssetByAssetIds(Long[] assetIds);

    /**
     * 删除数字资产信息
     * 
     * @param assetId 数字资产主键
     * @return 结果
     */
    public int deleteDigDigitalAssetByAssetId(Long assetId);

    List<DigDigitalAsset> queryAssetList(DigDigitalAssetQueryVo digitalAssetQueryVo);

    /**
     * 查询资产是否售罄
     */
    public int isSoldOut(Long assetId);

    String getCoverById(Long assetId);

    List<DigDigitalAsset> getUnconfiguredBoxList(List<Long> ids);

    List<DigAssetInventoryVo> getInventoryList(String saleType);

    void lockInventory(Long assetId, int quantity, String lockType, Long lockRelId);

    void unlockInventory(String inventoryLockTypeBlindBox, Long boxId);
}
