package com.ruoyi.issue.box.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.Version;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Data
public class DigUserBlindBox {


    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "用户盲盒ID")
    @TableId(type = IdType.AUTO)
    private Long userBoxId;


    @ApiModelProperty(value = "用户ID")
    private Long userId;


    @ApiModelProperty(value = "盲盒ID")
    private Long boxId;


    @ApiModelProperty(value = "盲盒唯一编号")
    private String boxCode;


    @ApiModelProperty(value = "获取方式")
    private String acquireType;


    @ApiModelProperty(value = "获取记录ID")
    private Long acquireRecordId;


    @ApiModelProperty(value = "是否开启")
    private String isOpened;


    @ApiModelProperty(value = "开启时间")
    private LocalDateTime openTime;


//    @Version
//    @ApiModelProperty(value = "乐观锁版本号", hidden = true)
//    private Integer version = 0;
//
//    // 公共字段
//
    @ApiModelProperty(value = "状态代码")
    private String statusCd;


    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;


    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateDate;

    @TableField(exist = false)
    @ApiModelProperty(value = "盲盒名称")
    private String boxName;

    @TableField(exist = false)
    @ApiModelProperty(value = "盲盒图片")
    private String boxCover;
}
