package com.ruoyi.issue.box.domain;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;

/**
 * 盲盒主对象 dig_blind_box
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
public class DigBlindBox extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 盲盒ID */
    private Long boxId;

    /** 关联资产ID */
    @Excel(name = "关联资产ID")
    private Long assetId;

    /** 盲盒名称 */
    @Excel(name = "盲盒名称")
    private String boxName;

    /** 盲盒封面URL */
    @Excel(name = "盲盒封面URL")
    private String boxCover;

    /** 盲盒描述 */
    @Excel(name = "盲盒描述")
    private String description;

    /** 开启开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开启开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /** 开启结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开启结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /** 状态代码 */
    @Excel(name = "状态代码")
    private String statusCd;

    /** 创建人 */
    @Excel(name = "创建人")
    private String createStaff;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;

    private String remark;

    /** 奖品列表 */
    @Excel(name = "奖品列表")
    @TableField(exist = false)
    private List<DigBlindBoxPrize> digBlindBoxPrizes;

    @TableField(exist = false)
    private String boxCode;
}
