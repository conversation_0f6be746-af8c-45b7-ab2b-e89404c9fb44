package com.ruoyi.issue.box.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.issue.common.entity.BaseEntity;
import lombok.Data;
import org.apache.poi.hpsf.Decimal;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 盲盒主对象 dig_blind_box
 * 
 * <AUTHOR>
 * @date 2025-07-24
 */
@Data
public class DigBlindBoxPrize extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 奖品ID */
    private Long prizeId;

    /** 关联盲盒ID */
    @Excel(name = "关联盲盒ID")
    private Long boxId;

    /** 奖品类型 */
    @Excel(name = "奖品类型")
    private String prizeType;

    /** 关联奖品ID */
    @Excel(name = "关联奖品ID")
    private Long prizeRelId;

    /** 奖品数量 */
    @Excel(name = "奖品数量")
    private int quantity;

    /** 中奖概率 */
    @Excel(name = "中奖概率")
    private BigDecimal probability;

    /**奖品名称*/
    @TableField(exist = false)
    private String assetName;

}
