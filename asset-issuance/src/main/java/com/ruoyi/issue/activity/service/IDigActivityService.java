package com.ruoyi.issue.activity.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.issue.activity.domain.DigActivity;
import com.ruoyi.issue.activity.domain.DigActivityParticipation;
import com.ruoyi.issue.coupon.vo.DigCouponCodeVo;
import com.ruoyi.system.domain.vo.ActivityRankVo;
import com.ruoyi.system.domain.DigUserInvitation;

/**
 * 活动Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface IDigActivityService extends IService<DigActivity>
{
    /**
     * 查询活动
     * 
     * @param activityId 活动主键
     * @return 活动
     */
    public DigActivity selectDigActivityByActivityId(Long activityId);

    /**
     * 查询活动列表
     * 
     * @param digActivity 活动
     * @return 活动集合
     */
    public List<DigActivity> selectDigActivityList(DigActivity digActivity);

    /**
     * 新增活动
     * 
     * @param digActivity 活动
     * @return 结果
     */
    public int insertDigActivity(DigActivity digActivity);

    /**
     * 修改活动
     * 
     * @param digActivity 活动
     * @return 结果
     */
    public int updateDigActivity(DigActivity digActivity);

    /**
     * 批量删除活动
     * 
     * @param activityIds 需要删除的活动主键集合
     * @return 结果
     */
    public int deleteDigActivityByActivityIds(Long[] activityIds);

    /**
     * 删除活动信息
     * 
     * @param activityId 活动主键
     * @return 结果
     */

    /**
     * 活动开奖
     */
    public void drawActivity(Long activityId);

    /**
     * 安全开奖方法 - 使用分布式锁防止重复开奖
     * 适用于大规模参与者的开奖场景
     */
    public void drawActivityWithLock(Long activityId);

    public int deleteDigActivityByActivityId(Long activityId);

    AjaxResult joinActivity(DigActivityParticipation digActivityParticipation);

    Boolean isParticipated(Long activityId, Long userId);

    DigUserInvitation getUserInvitation(String inviteCode,Long userId);

    void insertDigUserInvitation(DigUserInvitation digUserInvitation);

    List<ActivityRankVo> getInviteRank(Long activityId);

    List<DigCouponCodeVo> getDigActivityPrizes(Long activityId, Long userId);
}
