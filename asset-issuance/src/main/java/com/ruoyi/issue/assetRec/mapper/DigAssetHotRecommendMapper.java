package com.ruoyi.issue.assetRec.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.assetRec.domain.DigAssetHotRecommend;

/**
 * 资产热门推荐Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface DigAssetHotRecommendMapper extends BaseMapper<DigAssetHotRecommend>
{
    /**
     * 查询资产热门推荐
     * 
     * @param recId 资产热门推荐主键
     * @return 资产热门推荐
     */
    public DigAssetHotRecommend selectDigAssetHotRecommendByRecId(Long recId);

    /**
     * 查询资产热门推荐列表
     * 
     * @param digAssetHotRecommend 资产热门推荐
     * @return 资产热门推荐集合
     */
    public List<DigAssetHotRecommend> selectDigAssetHotRecommendList(DigAssetHotRecommend digAssetHotRecommend);

    /**
     * 新增资产热门推荐
     * 
     * @param digAssetHotRecommend 资产热门推荐
     * @return 结果
     */
    public int insertDigAssetHotRecommend(DigAssetHotRecommend digAssetHotRecommend);

    /**
     * 修改资产热门推荐
     * 
     * @param digAssetHotRecommend 资产热门推荐
     * @return 结果
     */
    public int updateDigAssetHotRecommend(DigAssetHotRecommend digAssetHotRecommend);

    /**
     * 删除资产热门推荐
     * 
     * @param recId 资产热门推荐主键
     * @return 结果
     */
    public int deleteDigAssetHotRecommendByRecId(Long recId);

    /**
     * 批量删除资产热门推荐
     * 
     * @param recIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigAssetHotRecommendByRecIds(Long[] recIds);
}
