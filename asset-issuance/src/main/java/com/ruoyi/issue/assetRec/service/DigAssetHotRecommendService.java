package com.ruoyi.issue.assetRec.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.assetRec.domain.DigAssetHotRecommend;

/**
 * 资产热门推荐Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface DigAssetHotRecommendService extends IService<DigAssetHotRecommend>
{
    /**
     * 查询资产热门推荐
     * 
     * @param recId 资产热门推荐主键
     * @return 资产热门推荐
     */
    public DigAssetHotRecommend selectDigAssetHotRecommendByRecId(Long recId);

    /**
     * 查询资产热门推荐列表
     * 
     * @param digAssetHotRecommend 资产热门推荐
     * @return 资产热门推荐集合
     */
    public List<DigAssetHotRecommend> selectDigAssetHotRecommendList(DigAssetHotRecommend digAssetHotRecommend);

    /**
     * 新增资产热门推荐
     * 
     * @param digAssetHotRecommend 资产热门推荐
     * @return 结果
     */
    public int insertDigAssetHotRecommend(DigAssetHotRecommend digAssetHotRecommend);

    /**
     * 修改资产热门推荐
     * 
     * @param digAssetHotRecommend 资产热门推荐
     * @return 结果
     */
    public int updateDigAssetHotRecommend(DigAssetHotRecommend digAssetHotRecommend);

    /**
     * 批量删除资产热门推荐
     * 
     * @param recIds 需要删除的资产热门推荐主键集合
     * @return 结果
     */
    public int deleteDigAssetHotRecommendByRecIds(Long[] recIds);

    /**
     * 删除资产热门推荐信息
     * 
     * @param recId 资产热门推荐主键
     * @return 结果
     */
    public int deleteDigAssetHotRecommendByRecId(Long recId);

    AjaxResult listUp(DigAssetHotRecommend digAssetHotRecommend);

    List<DigDigitalAsset> clientList(DigAssetHotRecommend digAssetHotRecommend);
}
