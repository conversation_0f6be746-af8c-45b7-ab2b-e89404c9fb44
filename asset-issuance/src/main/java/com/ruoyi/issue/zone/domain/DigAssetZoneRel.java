package com.ruoyi.issue.zone.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
@Data
@TableName("dig_asset_zone_rel")
public class DigAssetZoneRel extends Model<DigAssetZoneRel> {
    /**
     * 关联ID
     */
    @TableId(type= IdType.AUTO)
    private Long id;

    /**
     * 资产ID
     */
    @ApiModelProperty(value="资产ID")
    private Long assetId;

    /**
     * 专区ID
     */
    @ApiModelProperty(value="专区ID")
    private Long zoneId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createDate;
}
