package com.ruoyi.issue.zone.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.zone.domain.DigAssetZone;
import com.ruoyi.issue.zone.service.DigAssetZoneService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 资产专区Controller
 *
 * <AUTHOR>
 * @date 2025-06-10
 */

@Api(value = "digZone", tags = "资产专区管理")
@Slf4j
@RestController
@RequestMapping("/zone/zone")
public class DigAssetZoneController extends BaseController {
    @Autowired
    private DigAssetZoneService digAssetZoneService;

    @Autowired
    private AttachmentInfoService attachmentInfoService;

    /**
     * 查询资产专区列表
     */
    @PreAuthorize("@ss.hasPermi('zone:zone:list')")
    @GetMapping("/list")
    public TableDataInfo list(DigAssetZone digAssetZone) {
        startPage();
        List<DigAssetZone> list = digAssetZoneService.selectDigAssetZoneList(digAssetZone);
        return getDataTable(list);
    }

    /**
     * 导出资产专区列表
     */
    @PreAuthorize("@ss.hasPermi('zone:zone:export')")
    @Log(title = "资产专区", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DigAssetZone digAssetZone) {
        List<DigAssetZone> list = digAssetZoneService.selectDigAssetZoneList(digAssetZone);
        ExcelUtil<DigAssetZone> util = new ExcelUtil<DigAssetZone>(DigAssetZone.class);
        util.exportExcel(response, list, "资产专区数据");
    }

    /**
     * 获取资产专区详细信息
     */
    @PreAuthorize("@ss.hasPermi('zone:zone:query')")
    @GetMapping(value = "/{zoneId}")
    public AjaxResult getInfo(@PathVariable("zoneId") Long zoneId) {
        return success(digAssetZoneService.selectDigAssetZoneByZoneId(zoneId));
    }

    /**
     * 新增资产专区
     */
    @PreAuthorize("@ss.hasPermi('zone:zone:add')")
    @Log(title = "资产专区", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DigAssetZone digAssetZone) {
        digAssetZone.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        digAssetZone.setCreateStaff(String.valueOf(getUserId()));
        return toAjax(digAssetZoneService.insertDigAssetZone(digAssetZone));
    }

    /**
     * 修改资产专区
     */
    @PreAuthorize("@ss.hasPermi('zone:zone:edit')")
    @Log(title = "资产专区", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DigAssetZone digAssetZone) {
        digAssetZone.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        digAssetZone.setCreateStaff(String.valueOf(getUserId()));
        return toAjax(digAssetZoneService.updateDigAssetZone(digAssetZone));
    }

    /**
     * 删除资产专区
     */
    @PreAuthorize("@ss.hasPermi('zone:zone:remove')")
    @Log(title = "资产专区", businessType = BusinessType.DELETE)
    @DeleteMapping("/{zoneIds}")
    public AjaxResult remove(@PathVariable Long[] zoneIds) {
        return toAjax(digAssetZoneService.deleteDigAssetZoneByZoneIds(zoneIds));
    }

    /***
     * 查询拥有发薪平台专区运营权限的用户列表信息
     */
    @PreAuthorize("@ss.hasPermi('zone:zone:list')")
    @GetMapping("/list/digZoneOperate")
    public TableDataInfo digZoneOperateList(String operatorName) {
        List<DigAssetZone> list = digAssetZoneService.digZoneOperateList(operatorName);
        return getDataTable(list);
    }

    /***
     * 查询拥有发薪平台专区管理权限的用户列表信息
     */
    @PreAuthorize("@ss.hasPermi('zone:zone:list')")
    @GetMapping("/list/digZoneManage")
    public TableDataInfo digZoneManageList(String adminName) {
        return getDataTable(digAssetZoneService.digZoneManageList(adminName));
    }

    /**
     * 查询资产专区列表
     */
//    @PreAuthorize("@ss.hasPermi('zone:zone:list')")
    @GetMapping("/list/digZoneNameList")
    public AjaxResult digZoneNameList(String  zoneName) {
        return success(digAssetZoneService.digZoneNameList(zoneName,getUserId()));
    }

    /**-------------------------------客户端，不需要token的接口----------------------------------*/

    /**
     * 获取资产专区名称列表，不需要传token
     */
    @GetMapping("/listName")
    @Anonymous
    public AjaxResult listName() {
        return success(digAssetZoneService.selectNameList());
    }

    /**
     * 获取资产专区详细信息
     */
    @Anonymous
    @GetMapping(value = "/client/{zoneId}")
    public AjaxResult getZoneInfo(@PathVariable("zoneId") Long zoneId) {
        DigAssetZone digAssetZone = digAssetZoneService.selectDigAssetZoneByZoneId(zoneId);
        digAssetZone.setZoneCover(attachmentInfoService.getObjectUrl(digAssetZone.getZoneCover()));
        return success(digAssetZone);
    }
}
