package com.ruoyi.issue.banner.controller;

import java.time.LocalDateTime;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.issue.common.constant.Constants;
import com.ruoyi.scwt.file.service.AttachmentInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.issue.banner.domain.DiaBanner;
import com.ruoyi.issue.banner.service.IDiaBannerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * Banner图Controller
 *
 * <AUTHOR>
 * @date 2025-06-23
 */
@RestController
@Api(value = "Banner图管理", tags = "Banner图管理")
@RequestMapping("/banner/banner")
public class DiaBannerController extends BaseController {
    @Autowired
    private IDiaBannerService diaBannerService;

    @Autowired
    private AttachmentInfoService attachmentInfoService;

    /**
     * 查询Banner图列表
     */
    @PreAuthorize("@ss.hasPermi('banner:banner:list')")
    @GetMapping("/list")
    @ApiOperation("查询Banner图列表")
    public TableDataInfo list(DiaBanner diaBanner) {
        startPage();
        List<DiaBanner> list = diaBannerService.selectDiaBannerList(diaBanner);
        list.forEach(item -> {
            item.setImageUrl(attachmentInfoService.getObjectUrl(item.getImageUrl()));
        });
        return getDataTable(list);
    }

    /**
     * 导出Banner图列表
     */
    @PreAuthorize("@ss.hasPermi('banner:banner:export')")
    @Log(title = "Banner图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出Banner图列表")
    public void export(HttpServletResponse response, DiaBanner diaBanner) {
        List<DiaBanner> list = diaBannerService.selectDiaBannerList(diaBanner);
        ExcelUtil<DiaBanner> util = new ExcelUtil<DiaBanner>(DiaBanner.class);
        util.exportExcel(response, list, "Banner图数据");
    }

    /**
     * 获取Banner图详细信息
     */
    @PreAuthorize("@ss.hasPermi('banner:banner:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation("获取Banner图详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(diaBannerService.selectDiaBannerById(id));
    }

    /**
     * 新增Banner图
     */
    @PreAuthorize("@ss.hasPermi('banner:banner:add')")
    @Log(title = "Banner图", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增Banner图")
    public AjaxResult add(@RequestBody DiaBanner diaBanner) {
        diaBanner.setStatusCd(Constants.GENERAL_STATE_ENABLE);
        return toAjax(diaBannerService.insertDiaBanner(diaBanner));
    }

    /**
     * 修改Banner图
     */
    @PreAuthorize("@ss.hasPermi('banner:banner:edit')")
    @Log(title = "Banner图", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改Banner图")
    public AjaxResult edit(@RequestBody DiaBanner diaBanner) {
        return toAjax(diaBannerService.updateDiaBanner(diaBanner));
    }

    /**
     * 删除Banner图
     */
    @PreAuthorize("@ss.hasPermi('banner:banner:remove')")
    @Log(title = "Banner图", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除Banner图")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(diaBannerService.deleteDiaBannerByIds(ids));
    }

    /**
     * 查询可选择的资产列表
     */
    @PreAuthorize("@ss.hasPermi('banner:banner:list')")
    @GetMapping("/asset/list")
    @ApiOperation("查询可选择的资产列表")
    public AjaxResult assetList(DiaBanner diaBanner) {
        return diaBannerService.assetList(diaBanner);
    }

    /**
     * 查询可选择的活动列表
     */
    @PreAuthorize("@ss.hasPermi('banner:banner:list')")
    @GetMapping("/activity/list")
    @ApiOperation("查询可选择的活动列表")
    public AjaxResult activityList(DiaBanner diaBanner) {
        return diaBannerService.activityList(diaBanner);
    }


    /**------------------------------------------------客户端，不需要登录-------------------------------------------------*/
    /***
     * 获取Banner图列表
     */
    @GetMapping("/client/list")
    @Anonymous
    @ApiOperation("客户端获取Banner图列表")
    public AjaxResult clientList(DiaBanner diaBanner) {
        List<DiaBanner> list = diaBannerService.list(Wrappers.<DiaBanner>lambdaQuery()
                .eq(StringUtils.isNotEmpty(diaBanner.getJumpType()), DiaBanner::getJumpType, diaBanner.getJumpType())
                .eq(DiaBanner::getStatusCd, Constants.GENERAL_STATE_ENABLE)
                .le(DiaBanner::getStartTime, LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .ge(DiaBanner::getEndTime, LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")))
                .orderByDesc(DiaBanner::getSortOrder));
        list.forEach(item -> {
            item.setImageUrl(attachmentInfoService.getObjectUrl(item.getImageUrl()));
        });
        return success(list);
    }
}
