package com.ruoyi.issue.banner.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.issue.activity.domain.DigActivity;
import com.ruoyi.issue.activity.mapper.DigActivityMapper;
import com.ruoyi.issue.asset.domain.DigDigitalAsset;
import com.ruoyi.issue.asset.mapper.DigDigitalAssetMapper;
import com.ruoyi.issue.common.constant.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.issue.banner.mapper.DiaBannerMapper;
import com.ruoyi.issue.banner.domain.DiaBanner;
import com.ruoyi.issue.banner.service.IDiaBannerService;

/**
 * Banner图Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DiaBannerServiceImpl extends ServiceImpl<DiaBannerMapper, DiaBanner> implements IDiaBannerService
{

    private final DiaBannerMapper diaBannerMapper;

    private final DigDigitalAssetMapper digDigitalAssetMapper;
    private final DigActivityMapper digActivityMapper;

    /**
     * 查询Banner图
     * 
     * @param id Banner图主键
     * @return Banner图
     */
    @Override
    public DiaBanner selectDiaBannerById(Long id)
    {
        return diaBannerMapper.selectDiaBannerById(id);
    }

    /**
     * 查询Banner图列表
     * 
     * @param diaBanner Banner图
     * @return Banner图
     */
    @Override
    public List<DiaBanner> selectDiaBannerList(DiaBanner diaBanner)
    {
        return diaBannerMapper.selectDiaBannerList(diaBanner);
    }

    /**
     * 新增Banner图
     * 
     * @param diaBanner Banner图
     * @return 结果
     */
    @Override
    public int insertDiaBanner(DiaBanner diaBanner)
    {
        return diaBannerMapper.insertDiaBanner(diaBanner);
    }

    /**
     * 修改Banner图
     * 
     * @param diaBanner Banner图
     * @return 结果
     */
    @Override
    public int updateDiaBanner(DiaBanner diaBanner)
    {
        return diaBannerMapper.updateDiaBanner(diaBanner);
    }

    /**
     * 批量删除Banner图
     * 
     * @param ids 需要删除的Banner图主键
     * @return 结果
     */
    @Override
    public int deleteDiaBannerByIds(Long[] ids)
    {
        return diaBannerMapper.deleteDiaBannerByIds(ids);
    }

    /**
     * 删除Banner图信息
     * 
     * @param id Banner图主键
     * @return 结果
     */
    @Override
    public int deleteDiaBannerById(Long id)
    {
        return diaBannerMapper.deleteDiaBannerById(id);
    }

    @Override
    public AjaxResult assetList(DiaBanner diaBanner) {
//        List<Long> assetIds = diaBannerMapper.selectDiaBannerList(diaBanner).stream().map(DiaBanner::getContentId)..map(Long::parseLong).collect(Collectors.toList());
        List<DigDigitalAsset> digDigitalAssets = digDigitalAssetMapper.selectList(Wrappers.<DigDigitalAsset>lambdaQuery()
                .select(DigDigitalAsset::getAssetId, DigDigitalAsset::getAssetName)
//                .notIn(ObjectUtil.isNotEmpty(assetIds), DigDigitalAsset::getAssetId, assetIds)
                .eq(DigDigitalAsset::getStatusCd, Constants.DIG_ASSET_STATE_ENABLE)
                .like(StringUtils.isNotEmpty(diaBanner.getTitle()),DigDigitalAsset::getAssetName, diaBanner.getTitle()));
        return AjaxResult.success(digDigitalAssets);
    }

    @Override
    public AjaxResult activityList(DiaBanner diaBanner) {
        List<DigActivity> digActivities = digActivityMapper.selectList(Wrappers.<DigActivity>lambdaQuery()
                .select(DigActivity::getActivityId, DigActivity::getActivityName)
                .eq(DigActivity::getStatusCd, Constants.GENERAL_STATE_ENABLE)
                .like(StringUtils.isNotEmpty(diaBanner.getTitle()), DigActivity::getActivityName, diaBanner.getTitle()));
        return AjaxResult.success(digActivities);
    }
}
