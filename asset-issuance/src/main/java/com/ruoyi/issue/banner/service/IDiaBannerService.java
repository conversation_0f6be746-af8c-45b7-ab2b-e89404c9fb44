package com.ruoyi.issue.banner.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.issue.banner.domain.DiaBanner;

/**
 * Banner图Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-23
 */
public interface IDiaBannerService extends IService<DiaBanner>
{
    /**
     * 查询Banner图
     * 
     * @param id Banner图主键
     * @return Banner图
     */
    public DiaBanner selectDiaBannerById(Long id);

    /**
     * 查询Banner图列表
     * 
     * @param diaBanner Banner图
     * @return Banner图集合
     */
    public List<DiaBanner> selectDiaBannerList(DiaBanner diaBanner);

    /**
     * 新增Banner图
     * 
     * @param diaBanner Banner图
     * @return 结果
     */
    public int insertDiaBanner(DiaBanner diaBanner);

    /**
     * 修改Banner图
     * 
     * @param diaBanner Banner图
     * @return 结果
     */
    public int updateDiaBanner(DiaBanner diaBanner);

    /**
     * 批量删除Banner图
     * 
     * @param ids 需要删除的Banner图主键集合
     * @return 结果
     */
    public int deleteDiaBannerByIds(Long[] ids);

    /**
     * 删除Banner图信息
     * 
     * @param id Banner图主键
     * @return 结果
     */
    public int deleteDiaBannerById(Long id);

    AjaxResult assetList(DiaBanner diaBanner);

    AjaxResult activityList(DiaBanner diaBanner);
}
