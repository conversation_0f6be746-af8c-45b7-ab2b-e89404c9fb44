package com.ruoyi.issue.series.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.issue.series.domain.DigAssetSeries;

/**
 * 资产系列Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
public interface DigAssetSeriesMapper extends BaseMapper<DigAssetSeries>
{
    /**
     * 查询资产系列
     * 
     * @param seriesId 资产系列主键
     * @return 资产系列
     */
    public DigAssetSeries selectDigAssetSeriesBySeriesId(Long seriesId);

    /**
     * 查询资产系列列表
     * 
     * @param digAssetSeries 资产系列
     * @return 资产系列集合
     */
    public List<DigAssetSeries> selectDigAssetSeriesList(DigAssetSeries digAssetSeries);

    /**
     * 新增资产系列
     * 
     * @param digAssetSeries 资产系列
     * @return 结果
     */
    public int insertDigAssetSeries(DigAssetSeries digAssetSeries);

    /**
     * 修改资产系列
     * 
     * @param digAssetSeries 资产系列
     * @return 结果
     */
    public int updateDigAssetSeries(DigAssetSeries digAssetSeries);

    /**
     * 删除资产系列
     * 
     * @param seriesId 资产系列主键
     * @return 结果
     */
    public int deleteDigAssetSeriesBySeriesId(Long seriesId);

    /**
     * 批量删除资产系列
     * 
     * @param seriesIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDigAssetSeriesBySeriesIds(Long[] seriesIds);
}
