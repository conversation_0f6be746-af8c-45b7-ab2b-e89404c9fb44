package com.ruoyi.issue.quartz.util;

import com.ruoyi.issue.quartz.domain.NewJob;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionContext;

/**
 * 定时任务处理（禁止并发执行）
 * 
 * <AUTHOR>
 *
 */
@DisallowConcurrentExecution
public class QuartzDisallowConcurrentExecution extends AbstractQuartzJob
{
    @Override
    protected void doExecute(JobExecutionContext context, NewJob newJob) throws Exception
    {
        JobInvokeUtil.invokeMethod(newJob);
    }
}
