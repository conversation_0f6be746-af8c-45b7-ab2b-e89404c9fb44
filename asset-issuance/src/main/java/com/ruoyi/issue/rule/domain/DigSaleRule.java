package com.ruoyi.issue.rule.domain;

import java.time.LocalDateTime;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.issue.common.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;

/**
 * 发售规则对象 dig_sale_rule
 * 
 * <AUTHOR>
 * @date 2025-06-16
 */
@Data
@ApiModel(value = "发售规则对象")
public class DigSaleRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    @TableId
    /** 规则ID */
    @ApiModelProperty(value = "规则ID")
    private Long ruleId;

    /** 规则名称 */
    @Excel(name = "规则名称")
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /** 规则类型 */
    @Excel(name = "规则类型")
    @ApiModelProperty(value = "规则类型")
    private String ruleType;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /** 限购数量 */
    @Excel(name = "限购数量")
    @ApiModelProperty(value = "限购数量")
    private Long limitQuantity;

    /** 关联权益券ID */
    @Excel(name = "关联权益券ID")
    @ApiModelProperty(value = "关联权益券ID")
    private Long couponId;

    /** 关联权益券名称 */
    @Excel(name = "关联权益券名称")
    @TableField(exist = false)
    @ApiModelProperty(value = "关联权益券名称")
    private String couponName;

    /** 状态代码 */
    @Excel(name = "状态代码")
    @ApiModelProperty(value = "状态代码")
    private String statusCd;

    /** 状态时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "状态时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "状态时间")
    private LocalDateTime statusDate;

    /** 创建人 */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createStaff;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createDate;

    /** 修改人 */
    @Excel(name = "修改人")
    @ApiModelProperty(value = "修改人")
    private String updateStaff;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "修改时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateDate;

   @ApiModelProperty(value = "备注")
   private String remark;

   @TableField(exist = false)
   @ApiModelProperty(value = "资产ID")
   private Long assetId;
}
