<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.activity.mapper.DigActivityMapper">
    
    <resultMap type="DigActivity" id="DigActivityResult">
        <result property="activityId"    column="ACTIVITY_ID"    />
        <result property="activityName"    column="ACTIVITY_NAME"    />
        <result property="activityCover"    column="ACTIVITY_COVER"    />
        <result property="description"    column="DESCRIPTION"    />
        <result property="startTime"    column="START_TIME"    />
        <result property="endTime"    column="END_TIME"    />
        <result property="drawTime"    column="DRAW_TIME"    />
        <result property="statusCd"    column="STATUS_CD"    />
        <result property="createStaff"    column="CREATE_STAFF"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="updateStaff"    column="UPDATE_STAFF"    />
        <result property="updateDate"    column="UPDATE_DATE"    />
    </resultMap>

    <sql id="selectDigActivityVo">
        select ACTIVITY_ID, ACTIVITY_NAME, ACTIVITY_COVER, DESCRIPTION, START_TIME, END_TIME, DRAW_TIME, STATUS_CD, CREATE_STAFF, CREATE_DATE, UPDATE_STAFF, UPDATE_DATE from dig_activity
    </sql>

    <select id="selectDigActivityList" parameterType="DigActivity" resultMap="DigActivityResult">
        <include refid="selectDigActivityVo"/>
        <where>  
            <if test="activityName != null  and activityName != ''"> and ACTIVITY_NAME like concat('%', #{activityName}, '%')</if>
            <if test="activityCover != null  and activityCover != ''"> and ACTIVITY_COVER = #{activityCover}</if>
            <if test="description != null  and description != ''"> and DESCRIPTION = #{description}</if>
            <if test="startTime != null "> and START_TIME = #{startTime}</if>
            <if test="endTime != null "> and END_TIME = #{endTime}</if>
            <if test="drawTime != null "> and DRAW_TIME = #{drawTime}</if>
            <if test="statusCd != null  and statusCd != ''"> and STATUS_CD = #{statusCd}</if>
            <if test="createStaff != null  and createStaff != ''"> and CREATE_STAFF = #{createStaff}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="updateStaff != null  and updateStaff != ''"> and UPDATE_STAFF = #{updateStaff}</if>
            <if test="updateDate != null "> and UPDATE_DATE = #{updateDate}</if>
        </where>
        order by create_date desc
    </select>
    
    <select id="selectDigActivityByActivityId" parameterType="Long" resultMap="DigActivityResult">
        <include refid="selectDigActivityVo"/>
        where ACTIVITY_ID = #{activityId}
    </select>

    <insert id="insertDigActivity" parameterType="DigActivity" useGeneratedKeys="true" keyProperty="activityId">
        insert into dig_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="activityName != null and activityName != ''">ACTIVITY_NAME,</if>
            <if test="activityCover != null and activityCover != ''">ACTIVITY_COVER,</if>
            <if test="description != null and description != ''">DESCRIPTION,</if>
            <if test="startTime != null">START_TIME,</if>
            <if test="endTime != null">END_TIME,</if>
            <if test="drawTime != null">DRAW_TIME,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="createStaff != null">CREATE_STAFF,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateStaff != null">UPDATE_STAFF,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="activityName != null and activityName != ''">#{activityName},</if>
            <if test="activityCover != null and activityCover != ''">#{activityCover},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="drawTime != null">#{drawTime},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="createStaff != null">#{createStaff},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateStaff != null">#{updateStaff},</if>
            <if test="updateDate != null">#{updateDate},</if>
         </trim>
    </insert>

    <update id="updateDigActivity" parameterType="DigActivity">
        update dig_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="activityName != null and activityName != ''">ACTIVITY_NAME = #{activityName},</if>
            <if test="activityCover != null and activityCover != ''">ACTIVITY_COVER = #{activityCover},</if>
            <if test="description != null and description != ''">DESCRIPTION = #{description},</if>
            <if test="startTime != null">START_TIME = #{startTime},</if>
            <if test="endTime != null">END_TIME = #{endTime},</if>
            <if test="drawTime != null">DRAW_TIME = #{drawTime},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="createStaff != null">CREATE_STAFF = #{createStaff},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateStaff != null">UPDATE_STAFF = #{updateStaff},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
        </trim>
        where ACTIVITY_ID = #{activityId}
    </update>

    <delete id="deleteDigActivityByActivityId" parameterType="Long">
        delete from dig_activity where ACTIVITY_ID = #{activityId}
    </delete>

    <delete id="deleteDigActivityByActivityIds" parameterType="String">
        delete from dig_activity where ACTIVITY_ID in 
        <foreach item="activityId" collection="array" open="(" separator="," close=")">
            #{activityId}
        </foreach>
    </delete>
</mapper>