<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.assetData.mapper.DigDataAssetMapper">
    
    <resultMap type="DigDataAsset" id="DigDataAssetResult">
        <result property="dataAssetId"    column="DATA_ASSET_ID"    />
        <result property="userId"    column="USER_ID"    />
        <result property="acquireType"    column="ACQUIRE_TYPE"    />
        <result property="acquireRecordId"    column="ACQUIRE_RECORD_ID"    />
        <result property="assetId"    column="ASSET_ID"    />
        <result property="assetName"    column="ASSET_NAME"    />
        <result property="assetCode"    column="ASSET_CODE"    />
        <result property="chainId"    column="CHAIN_ID"    />
        <result property="chainHash"    column="CHAIN_HASH"    />
        <result property="chainTime"    column="CHAIN_TIME"    />
        <result property="isRedeemed"    column="IS_REDEEMED"    />
        <result property="VERSION"    column="VERSION"    />
        <result property="statusCd"    column="STATUS_CD"    />
        <result property="statusDate"    column="STATUS_DATE"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="updateDate"    column="UPDATE_DATE"    />
    </resultMap>

    <sql id="selectDigDataAssetVo">
        select DATA_ASSET_ID, USER_ID, ACQUIRE_TYPE, ACQUIRE_RECORD_ID, ASSET_ID, ASSET_NAME, ASSET_CODE, CHAIN_ID, CHAIN_HASH, CHAIN_TIME, IS_REDEEMED, VERSION, STATUS_CD, STATUS_DATE, CREATE_DATE, UPDATE_DATE from dig_data_asset
    </sql>

    <select id="selectDigDataAssetList" parameterType="DigDataAsset" resultMap="DigDataAssetResult">
        <include refid="selectDigDataAssetVo"/>
        <where>  
            <if test="userId != null "> and USER_ID = #{userId}</if>
            <if test="acquireType != null  and acquireType != ''"> and ACQUIRE_TYPE = #{acquireType}</if>
            <if test="acquireRecordId != null "> and ACQUIRE_RECORD_ID = #{acquireRecordId}</if>
            <if test="assetId != null "> and ASSET_ID = #{assetId}</if>
            <if test="assetName != null  and assetName != ''"> and ASSET_NAME like concat('%', #{assetName}, '%')</if>
            <if test="assetCode != null  and assetCode != ''"> and ASSET_CODE = #{assetCode}</if>
            <if test="chainId != null  and chainId != ''"> and CHAIN_ID = #{chainId}</if>
            <if test="chainHash != null  and chainHash != ''"> and CHAIN_HASH = #{chainHash}</if>
            <if test="chainTime != null "> and CHAIN_TIME = #{chainTime}</if>
            <if test="isRedeemed != null "> and IS_REDEEMED = #{isRedeemed}</if>
            <if test="VERSION != null "> and VERSION = #{VERSION}</if>
            <if test="statusCd != null  and statusCd != ''"> and STATUS_CD = #{statusCd}</if>
            <if test="statusDate != null "> and STATUS_DATE = #{statusDate}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="updateDate != null "> and UPDATE_DATE = #{updateDate}</if>
        </where>
    </select>
    
    <select id="selectDigDataAssetByDataAssetId" parameterType="Long" resultMap="DigDataAssetResult">
        <include refid="selectDigDataAssetVo"/>
        where DATA_ASSET_ID = #{dataAssetId}
    </select>

    <insert id="insertDigDataAsset" parameterType="DigDataAsset" useGeneratedKeys="true" keyProperty="dataAssetId">
        insert into dig_data_asset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">USER_ID,</if>
            <if test="acquireType != null and acquireType != ''">ACQUIRE_TYPE,</if>
            <if test="acquireRecordId != null">ACQUIRE_RECORD_ID,</if>
            <if test="assetId != null">ASSET_ID,</if>
            <if test="assetName != null and assetName != ''">ASSET_NAME,</if>
            <if test="assetCode != null and assetCode != ''">ASSET_CODE,</if>
            <if test="chainId != null">CHAIN_ID,</if>
            <if test="chainHash != null">CHAIN_HASH,</if>
            <if test="chainTime != null">CHAIN_TIME,</if>
            <if test="isRedeemed != null">IS_REDEEMED,</if>
            <if test="VERSION != null">VERSION,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="statusDate != null">STATUS_DATE,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="acquireType != null and acquireType != ''">#{acquireType},</if>
            <if test="acquireRecordId != null">#{acquireRecordId},</if>
            <if test="assetId != null">#{assetId},</if>
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="assetCode != null and assetCode != ''">#{assetCode},</if>
            <if test="chainId != null">#{chainId},</if>
            <if test="chainHash != null">#{chainHash},</if>
            <if test="chainTime != null">#{chainTime},</if>
            <if test="isRedeemed != null">#{isRedeemed},</if>
            <if test="VERSION != null">#{VERSION},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="statusDate != null">#{statusDate},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
         </trim>
    </insert>

    <update id="updateDigDataAsset" parameterType="DigDataAsset">
        update dig_data_asset
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">USER_ID = #{userId},</if>
            <if test="acquireType != null and acquireType != ''">ACQUIRE_TYPE = #{acquireType},</if>
            <if test="acquireRecordId != null">ACQUIRE_RECORD_ID = #{acquireRecordId},</if>
            <if test="assetId != null">ASSET_ID = #{assetId},</if>
            <if test="assetName != null and assetName != ''">ASSET_NAME = #{assetName},</if>
            <if test="assetCode != null and assetCode != ''">ASSET_CODE = #{assetCode},</if>
            <if test="chainId != null">CHAIN_ID = #{chainId},</if>
            <if test="chainHash != null">CHAIN_HASH = #{chainHash},</if>
            <if test="chainTime != null">CHAIN_TIME = #{chainTime},</if>
            <if test="isRedeemed != null">IS_REDEEMED = #{isRedeemed},</if>
            <if test="VERSION != null">VERSION = #{VERSION},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="statusDate != null">STATUS_DATE = #{statusDate},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
        </trim>
        where DATA_ASSET_ID = #{dataAssetId}
    </update>

    <delete id="deleteDigDataAssetByDataAssetId" parameterType="Long">
        delete from dig_data_asset where DATA_ASSET_ID = #{dataAssetId}
    </delete>

    <delete id="deleteDigDataAssetByDataAssetIds" parameterType="String">
        delete from dig_data_asset where DATA_ASSET_ID in 
        <foreach item="dataAssetId" collection="array" open="(" separator="," close=")">
            #{dataAssetId}
        </foreach>
    </delete>
</mapper>