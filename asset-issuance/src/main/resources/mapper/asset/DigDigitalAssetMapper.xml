<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.asset.mapper.DigDigitalAssetMapper">
    
    <resultMap type="DigDigitalAsset" id="DigDigitalAssetResult">
        <result property="assetId"    column="ASSET_ID"    />
        <result property="assetName"    column="ASSET_NAME"    />
        <result property="assetCover"    column="ASSET_COVER"    />
        <result property="assetCoverThumbnail"    column="ASSET_COVER_THUMBNAIL"    />
        <result property="assetFile"    column="ASSET_FILE"    />
        <result property="issuerIds"    column="ISSUER_IDS"    />
        <result property="assetType"    column="ASSET_TYPE"    />
        <result property="assetLevel"    column="ASSET_LEVEL"    />
        <result property="saleStartTime"    column="SALE_START_TIME"    />
        <result property="isBlindBox"    column="IS_BLIND_BOX"    />
        <result property="issueQuantity"    column="ISSUE_QUANTITY"    />
        <result property="individualLimit"    column="INDIVIDUAL_LIMIT"    />
        <result property="enterpriseLimit"    column="ENTERPRISE_LIMIT"    />
        <result property="airdropQuantity"    column="AIRDROP_QUANTITY"    />
        <result property="activityQuantity"    column="ACTIVITY_QUANTITY"    />
        <result property="issuePrice"    column="ISSUE_PRICE"    />
        <result property="seriesId"    column="SERIES_ID"    />
        <result property="assetKeywords"    column="ASSET_KEYWORDS"    />
        <result property="assetDesc"    column="ASSET_DESC"    />
        <result property="introImages"    column="INTRO_IMAGES"    />
        <result property="rejectionReason"    column="REJECTION_REASON"    />
        <result property="statusCd"    column="STATUS_CD"    />
        <result property="statusDate"    column="STATUS_DATE"    />
        <result property="createStaff"    column="CREATE_STAFF"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="updateStaff"    column="UPDATE_STAFF"    />
        <result property="updateDate"    column="UPDATE_DATE"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectDigDigitalAssetVo">
        select ASSET_ID, ASSET_NAME, ASSET_COVER, ASSET_COVER_THUMBNAIL, ASSET_FILE, ISSUER_IDS, ASSET_TYPE, ASSET_LEVEL, SALE_START_TIME, IS_BLIND_BOX, ISSUE_QUANTITY, INDIVIDUAL_LIMIT, ENTERPRISE_LIMIT, AIRDROP_QUANTITY, ACTIVITY_QUANTITY, ISSUE_PRICE, SERIES_ID, ASSET_KEYWORDS, ASSET_DESC, INTRO_IMAGES, REJECTION_REASON, STATUS_CD, STATUS_DATE, CREATE_STAFF, CREATE_DATE, UPDATE_STAFF, UPDATE_DATE, remark from dig_digital_asset
    </sql>

    <select id="selectDigDigitalAssetList" parameterType="DigDigitalAsset" resultMap="DigDigitalAssetResult">
        <include refid="selectDigDigitalAssetVo"/>
        <where>  
            <if test="assetName != null  and assetName != ''"> and ASSET_NAME like concat('%', #{assetName}, '%')</if>
            <if test="assetCover != null  and assetCover != ''"> and ASSET_COVER = #{assetCover}</if>
             <if test="assetCoverThumbnail != null  and assetCoverThumbnail != ''"> and ASSET_COVER_THUMBNAIL = #{assetCoverThumbnail}</if>
            <if test="assetFile != null  and assetFile != ''"> and ASSET_FILE = #{assetFile}</if>
            <if test="issuerIds != null  and issuerIds != ''"> and ISSUER_IDS = #{issuerIds}</if>
            <if test="assetType != null  and assetType != ''"> and ASSET_TYPE = #{assetType}</if>
            <if test="assetLevel != null "> and ASSET_LEVEL = #{assetLevel}</if>
            <if test="saleStartTime != null "> and SALE_START_TIME = #{saleStartTime}</if>
             <if test="isBlindBox != null "> and IS_BLIND_BOX = #{isBlindBox}</if>
            <if test="issueQuantity != null "> and ISSUE_QUANTITY = #{issueQuantity}</if>
            <if test="individualLimit != null "> and INDIVIDUAL_LIMIT = #{individualLimit}</if>
            <if test="enterpriseLimit != null "> and ENTERPRISE_LIMIT = #{enterpriseLimit}</if>
            <if test="airdropQuantity != null "> and AIRDROP_QUANTITY = #{airdropQuantity}</if>
            <if test="activityQuantity != null "> and ACTIVITY_QUANTITY = #{activityQuantity}</if>
            <if test="issuePrice != null "> and ISSUE_PRICE = #{issuePrice}</if>
            <if test="seriesId != null "> and SERIES_ID = #{seriesId}</if>
            <if test="assetKeywords != null  and assetKeywords != ''"> and ASSET_KEYWORDS = #{assetKeywords}</if>
            <if test="assetDesc != null  and assetDesc != ''"> and ASSET_DESC = #{assetDesc}</if>
            <if test="introImages != null  and introImages != ''"> and INTRO_IMAGES = #{introImages}</if>
            <if test="rejectionReason != null  and rejectionReason != ''"> and REJECTION_REASON = #{rejectionReason}</if>
            <if test="statusCd != null  and statusCd != ''"> and STATUS_CD = #{statusCd}</if>
            <if test="statusDate != null "> and STATUS_DATE = #{statusDate}</if>
            <if test="createStaff != null  and createStaff != ''"> and CREATE_STAFF = #{createStaff}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="updateStaff != null  and updateStaff != ''"> and UPDATE_STAFF = #{updateStaff}</if>
            <if test="updateDate != null "> and UPDATE_DATE = #{updateDate}</if>
            <if test="remark != null  and remark != ''"> and remark = #{remark}</if>
        </where>
    </select>
    
    <select id="selectDigDigitalAssetByAssetId" parameterType="Long" resultMap="DigDigitalAssetResult">
        <include refid="selectDigDigitalAssetVo"/>
        where ASSET_ID = #{assetId}
    </select>

    <insert id="insertDigDigitalAsset" parameterType="DigDigitalAsset" useGeneratedKeys="true" keyProperty="assetId">
        insert into dig_digital_asset
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="assetName != null and assetName != ''">ASSET_NAME,</if>
            <if test="assetCover != null and assetCover != ''">ASSET_COVER,</if>
            <if test="assetCoverThumbnail != null  and assetCoverThumbnail != ''"> ASSET_COVER_THUMBNAIL</if>
            <if test="assetFile != null and assetFile != ''">ASSET_FILE,</if>
            <if test="issuerIds != null and issuerIds != ''">ISSUER_IDS,</if>
            <if test="assetType != null and assetType != ''">ASSET_TYPE,</if>
            <if test="assetLevel != null">ASSET_LEVEL,</if>
            <if test="saleStartTime != null">SALE_START_TIME,</if>
            <if test="isBlindBox != null">IS_BLIND_BOX,</if>
            <if test="issueQuantity != null">ISSUE_QUANTITY,</if>
            <if test="individualLimit != null">INDIVIDUAL_LIMIT,</if>
            <if test="enterpriseLimit != null">ENTERPRISE_LIMIT,</if>
            <if test="airdropQuantity != null">AIRDROP_QUANTITY,</if>
            <if test="activityQuantity != null">ACTIVITY_QUANTITY,</if>
            <if test="issuePrice != null">ISSUE_PRICE,</if>
            <if test="seriesId != null">SERIES_ID,</if>
            <if test="assetKeywords != null">ASSET_KEYWORDS,</if>
            <if test="assetDesc != null">ASSET_DESC,</if>
            <if test="introImages != null">INTRO_IMAGES,</if>
            <if test="rejectionReason != null">REJECTION_REASON,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="statusDate != null">STATUS_DATE,</if>
            <if test="createStaff != null">CREATE_STAFF,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateStaff != null">UPDATE_STAFF,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="assetName != null and assetName != ''">#{assetName},</if>
            <if test="assetCover != null and assetCover != ''">#{assetCover},</if>
            <if test="assetCoverThumbnail != null and assetCoverThumbnail != ''">#{assetCoverThumbnail},</if>
            <if test="assetFile != null and assetFile != ''">#{assetFile},</if>
            <if test="issuerIds != null and issuerIds != ''">#{issuerIds},</if>
            <if test="assetType != null and assetType != ''">#{assetType},</if>
            <if test="assetLevel != null">#{assetLevel},</if>
            <if test="saleStartTime != null">#{saleStartTime},</if>
            <if test="isBlindBox != null">#{isBlindBox},</if>
            <if test="issueQuantity != null">#{issueQuantity},</if>
            <if test="individualLimit != null">#{individualLimit},</if>
            <if test="enterpriseLimit != null">#{enterpriseLimit},</if>
            <if test="airdropQuantity != null">#{airdropQuantity},</if>
            <if test="activityQuantity != null">#{activityQuantity},</if>
            <if test="issuePrice != null">#{issuePrice},</if>
            <if test="seriesId != null">#{seriesId},</if>
            <if test="assetKeywords != null">#{assetKeywords},</if>
            <if test="assetDesc != null">#{assetDesc},</if>
            <if test="introImages != null">#{introImages},</if>
            <if test="rejectionReason != null">#{rejectionReason},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="statusDate != null">#{statusDate},</if>
            <if test="createStaff != null">#{createStaff},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateStaff != null">#{updateStaff},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateDigDigitalAsset" parameterType="DigDigitalAsset">
        update dig_digital_asset
        <trim prefix="SET" suffixOverrides=",">
            <if test="assetName != null and assetName != ''">ASSET_NAME = #{assetName},</if>
            <if test="assetCover != null and assetCover != ''">ASSET_COVER = #{assetCover},</if>
            <if test="assetCoverThumbnail != null  and assetCoverThumbnail != ''"> and ASSET_COVER_THUMBNAIL = #{assetCoverThumbnail}</if>
            <if test="assetFile != null and assetFile != ''">ASSET_FILE = #{assetFile},</if>
            <if test="issuerIds != null and issuerIds != ''">ISSUER_IDS = #{issuerIds},</if>
            <if test="assetType != null and assetType != ''">ASSET_TYPE = #{assetType},</if>
            <if test="assetLevel != null">ASSET_LEVEL = #{assetLevel},</if>
            <if test="saleStartTime != null">SALE_START_TIME = #{saleStartTime},</if>
            <if test="isBlindBox != null">IS_BLIND_BOX = #{isBlindBox},</if>
            <if test="issueQuantity != null">ISSUE_QUANTITY = #{issueQuantity},</if>
            <if test="individualLimit != null">INDIVIDUAL_LIMIT = #{individualLimit},</if>
            <if test="enterpriseLimit != null">ENTERPRISE_LIMIT = #{enterpriseLimit},</if>
            <if test="airdropQuantity != null">AIRDROP_QUANTITY = #{airdropQuantity},</if>
            <if test="activityQuantity != null">ACTIVITY_QUANTITY = #{activityQuantity},</if>
            <if test="issuePrice != null">ISSUE_PRICE = #{issuePrice},</if>
            <if test="seriesId != null">SERIES_ID = #{seriesId},</if>
            <if test="assetKeywords != null">ASSET_KEYWORDS = #{assetKeywords},</if>
            <if test="assetDesc != null">ASSET_DESC = #{assetDesc},</if>
            <if test="introImages != null">INTRO_IMAGES = #{introImages},</if>
            <if test="rejectionReason != null">REJECTION_REASON = #{rejectionReason},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="statusDate != null">STATUS_DATE = #{statusDate},</if>
            <if test="createStaff != null">CREATE_STAFF = #{createStaff},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateStaff != null">UPDATE_STAFF = #{updateStaff},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where ASSET_ID = #{assetId}
    </update>

    <delete id="deleteDigDigitalAssetByAssetId" parameterType="Long">
        delete from dig_digital_asset where ASSET_ID = #{assetId}
    </delete>

    <delete id="deleteDigDigitalAssetByAssetIds" parameterType="String">
        delete from dig_digital_asset where ASSET_ID in 
        <foreach item="assetId" collection="array" open="(" separator="," close=")">
            #{assetId}
        </foreach>
    </delete>
</mapper>