<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.asset.mapper.DigAssetInventoryMapper">


    <insert id="insertDigAssetInventoryBatch"  useGeneratedKeys="true" keyProperty="inventoryId">
        insert into dig_asset_inventory (ASSET_ID, ASSET_CODE, STORAGE_TIME, SALE_TYPE, STATUS_CD, REMARK) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.assetId}, #{item.assetCode}, #{item.storageTime}, #{item.saleType}, #{item.statusCd}, #{item.remark})
        </foreach>
    </insert>
    <update id="updateStatusByCode">
        update dig_asset_inventory set status_cd = #{statusCd} where asset_code = #{assetCode}
    </update>
    <select id="getInventoryList" resultType="com.ruoyi.issue.asset.domain.DigAssetInventoryVo">
        SELECT
            asset_id AS assetId,
            COUNT( ASSET_CODE ) AS inventory
        FROM
            dig_asset_inventory
        WHERE
            sale_type = #{saleType}
          AND STATUS_CD = '0'
        GROUP BY
            ASSET_ID
    </select>
</mapper>