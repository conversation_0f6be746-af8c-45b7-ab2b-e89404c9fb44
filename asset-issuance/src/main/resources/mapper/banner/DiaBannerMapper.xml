<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.banner.mapper.DiaBannerMapper">
    
    <resultMap type="DiaBanner" id="DiaBannerResult">
        <result property="id"    column="id"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="jumpType"    column="jump_type"    />
        <result property="contentId"    column="content_id"    />
        <result property="title"    column="title"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="statusCd"    column="STATUS_CD"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createDate"    column="CREATE_DATE"    />
        <result property="updateDate"    column="UPDATE_DATE"    />
    </resultMap>

    <sql id="selectDiaBannerVo">
        select id, image_url, jump_type, content_id, title, sort_order, STATUS_CD, start_time, end_time, CREATE_DATE, UPDATE_DATE from dia_banner
    </sql>

    <select id="selectDiaBannerList" parameterType="DiaBanner" resultMap="DiaBannerResult">
        <include refid="selectDiaBannerVo"/>
        <where>  
            <if test="imageUrl != null  and imageUrl != ''"> and image_url = #{imageUrl}</if>
            <if test="jumpType != null "> and jump_type = #{jumpType}</if>
            <if test="contentId != null  and contentId != ''"> and content_id = #{contentId}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
            <if test="statusCd != null "> and STATUS_CD = #{statusCd}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="createDate != null "> and CREATE_DATE = #{createDate}</if>
            <if test="updateDate != null "> and UPDATE_DATE = #{updateDate}</if>
        </where>
    </select>
    
    <select id="selectDiaBannerById" parameterType="Long" resultMap="DiaBannerResult">
        <include refid="selectDiaBannerVo"/>
        where id = #{id}
    </select>

    <insert id="insertDiaBanner" parameterType="DiaBanner" useGeneratedKeys="true" keyProperty="id">
        insert into dia_banner
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="imageUrl != null and imageUrl != ''">image_url,</if>
            <if test="jumpType != null">jump_type,</if>
            <if test="contentId != null">content_id,</if>
            <if test="title != null">title,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="imageUrl != null and imageUrl != ''">#{imageUrl},</if>
            <if test="jumpType != null">#{jumpType},</if>
            <if test="contentId != null">#{contentId},</if>
            <if test="title != null">#{title},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateDate != null">#{updateDate},</if>
         </trim>
    </insert>

    <update id="updateDiaBanner" parameterType="DiaBanner">
        update dia_banner
        <trim prefix="SET" suffixOverrides=",">
            <if test="imageUrl != null and imageUrl != ''">image_url = #{imageUrl},</if>
            <if test="jumpType != null">jump_type = #{jumpType},</if>
            <if test="contentId != null">content_id = #{contentId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDiaBannerById" parameterType="Long">
        delete from dia_banner where id = #{id}
    </delete>

    <delete id="deleteDiaBannerByIds" parameterType="String">
        delete from dia_banner where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>