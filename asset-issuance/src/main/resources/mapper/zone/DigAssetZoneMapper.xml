<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.issue.zone.mapper.DigAssetZoneMapper">

    <resultMap type="DigAssetZone" id="DigAssetZoneResult">
        <result property="zoneId" column="ZONE_ID"/>
        <result property="zoneCover" column="ZONE_COVER"/>
        <result property="zoneName" column="ZONE_NAME"/>
        <result property="zoneDesc" column="ZONE_DESC"/>
        <result property="adminId" column="ADMIN_ID"/>
        <result property="adminName" column="ADMIN_NAME"/>
        <result property="adminPhone" column="ADMIN_PHONE"/>
        <result property="operatorId" column="OPERATOR_ID"/>
        <result property="operatorName" column="OPERATOR_NAME"/>
        <result property="operatorPhone" column="OPERATOR_PHONE"/>
        <result property="statusCd" column="STATUS_CD"/>
        <result property="statusDate" column="STATUS_DATE"/>
        <result property="createStaff" column="CREATE_STAFF"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateStaff" column="UPDATE_STAFF"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="remark" column="remark"/>
        <result property="unionPayMerchantCode" column="UNION_PAY_MERCHANT_CODE"/>
        <result property="payAccountId" column="PAY_ACCOUNT_ID"/>
    </resultMap>

    <sql id="selectDigAssetZoneVo">
        select ZONE_ID,
               ZONE_COVER,
               ZONE_NAME,
               ZONE_DESC,
               ADMIN_ID,
               ADMIN_NAME,
                ADMIN_PHONE,
               OPERATOR_ID,
               OPERATOR_NAME,
               OPERATOR_PHONE,
               STATUS_CD,
               STATUS_DATE,
               CREATE_STAFF,
               CREATE_DATE,
               UPDATE_STAFF,
               UPDATE_DATE,
               remark,
               UNION_PAY_MERCHANT_CODE,
               PAY_ACCOUNT_ID
        from dig_asset_zone
    </sql>

    <select id="selectDigAssetZoneList" parameterType="DigAssetZone" resultMap="DigAssetZoneResult">
        <include refid="selectDigAssetZoneVo"/>
        <where>
            <if test="zoneCover != null  and zoneCover != ''">and ZONE_COVER = #{zoneCover}</if>
            <if test="zoneName != null  and zoneName != ''">and ZONE_NAME like concat('%', #{zoneName}, '%')</if>
            <if test="zoneDesc != null  and zoneDesc != ''">and ZONE_DESC like concat('%', #{zoneDesc}, '%')</if>

            <if test="operatorId != null ">and OPERATOR_ID = #{operatorId}</if>
            <if test="operatorName != null  and operatorName != ''">and OPERATOR_NAME like concat('%', #{operatorName},
                '%')
            </if>
            <if test="operatorPhone != null  and operatorPhone != ''">and OPERATOR_PHONE = #{operatorPhone}</if>
            <if test="adminId != null ">and ADMIN_ID = #{adminId}</if>
            <if test="adminName != null  and adminName != ''">and ADMIN_NAME like concat('%', #{adminName}, '%')</if>
            <if test="adminPhone != null  and adminPhone != ''">and ADMIN_PHONE = #{adminPhone}</if>
            <if test="statusCd != null  and statusCd != ''">and STATUS_CD = #{statusCd}</if>
            <if test="statusDate != null ">and STATUS_DATE = #{statusDate}</if>
            <if test="params.beginCreateDate != null and params.beginCreateDate != '' and params.endCreateDate != null and params.endCreateDate != ''">
                and CREATE_DATE between #{params.beginCreateDate} and #{params.endCreateDate}
            </if>
            <if test="remark != null  and remark != ''">and remark = #{remark}</if>
            <if test="unionPayMerchantCode != null  and unionPayMerchantCode != ''">and UNION_PAY_MERCHANT_CODE =
                #{unionPayMerchantCode}
            </if>
            <if test="payAccountId != null ">and PAY_ACCOUNT_ID = #{payAccountId}</if>
        </where>
    </select>

    <select id="selectDigAssetZoneByZoneId" parameterType="Long" resultMap="DigAssetZoneResult">
        <include refid="selectDigAssetZoneVo"/>
        where ZONE_ID = #{zoneId}
    </select>
    <select id="digZoneOperateList" resultType="com.ruoyi.issue.zone.domain.DigAssetZone">
        SELECT
        r.user_id 'operatorId',
        i.ID_NAME 'operatorName',
        i.PHONE 'operatorPhone'
        FROM
        `sys_user_role` AS r
        LEFT JOIN identify AS i ON r.user_id = i.USER_ID
        WHERE
        r.role_id = '104'
        <if test="operatorName != null and operatorName != ''">
            AND i.ID_NAME LIKE CONCAT('%', #{operatorName}, '%')
        </if>

    </select>
    <select id="digZoneManageList" resultType="com.ruoyi.issue.zone.domain.DigAssetZone">
        SELECT
        r.user_id 'adminId',
        i.ID_NAME 'adminName',
        i.PHONE 'adminPhone'
        FROM
        `sys_user_role` AS r
        LEFT JOIN identify AS i ON r.user_id = i.USER_ID
        WHERE
        r.role_id = '105'
        <if test="adminName != null and adminName != ''">
            AND i.ID_NAME LIKE CONCAT('%', #{adminName}, '%')
        </if>
    </select>

    <insert id="insertDigAssetZone" parameterType="DigAssetZone" useGeneratedKeys="true" keyProperty="zoneId">
        insert into dig_asset_zone
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="zoneCover != null and zoneCover != ''">ZONE_COVER,</if>
            <if test="zoneName != null and zoneName != ''">ZONE_NAME,</if>
            <if test="zoneDesc != null and zoneDesc != ''">ZONE_DESC,</if>
            <if test="adminId != null">ADMIN_ID,</if>
            <if test="adminName != null and adminName != ''">ADMIN_NAME,</if>
            <if test="adminPhone != null and adminPhone != ''">ADMIN_PHONE,</if>
            <if test="operatorId != null">OPERATOR_ID,</if>
            <if test="operatorName != null and operatorName != ''">OPERATOR_NAME,</if>
            <if test="operatorPhone != null and operatorPhone != ''">OPERATOR_PHONE,</if>
            <if test="statusCd != null">STATUS_CD,</if>
            <if test="statusDate != null">STATUS_DATE,</if>
            <if test="createStaff != null">CREATE_STAFF,</if>
            <if test="createDate != null">CREATE_DATE,</if>
            <if test="updateStaff != null">UPDATE_STAFF,</if>
            <if test="updateDate != null">UPDATE_DATE,</if>
            <if test="remark != null">remark,</if>
            <if test="unionPayMerchantCode != null and unionPayMerchantCode != ''">UNION_PAY_MERCHANT_CODE,</if>
            <if test="payAccountId != null">PAY_ACCOUNT_ID,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="zoneCover != null and zoneCover != ''">#{zoneCover},</if>
            <if test="zoneName != null and zoneName != ''">#{zoneName},</if>
            <if test="zoneDesc != null and zoneDesc != ''">#{zoneDesc},</if>
            <if test="adminId != null">#{adminId},</if>
            <if test="adminName != null and adminName != ''">#{adminName},</if>
            <if test="adminPhone != null and adminPhone != ''">#{adminPhone},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="operatorName != null and operatorName != ''">#{operatorName},</if>
            <if test="operatorPhone != null and operatorPhone != ''">#{operatorPhone},</if>
            <if test="statusCd != null">#{statusCd},</if>
            <if test="statusDate != null">#{statusDate},</if>
            <if test="createStaff != null">#{createStaff},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateStaff != null">#{updateStaff},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="unionPayMerchantCode != null and unionPayMerchantCode != ''">#{unionPayMerchantCode},</if>
            <if test="payAccountId != null">#{payAccountId},</if>
        </trim>
    </insert>

    <update id="updateDigAssetZone" parameterType="DigAssetZone">
        update dig_asset_zone
        <trim prefix="SET" suffixOverrides=",">
            <if test="zoneCover != null and zoneCover != ''">ZONE_COVER = #{zoneCover},</if>
            <if test="zoneName != null and zoneName != ''">ZONE_NAME = #{zoneName},</if>
            <if test="zoneDesc != null and zoneDesc != ''">ZONE_DESC = #{zoneDesc},</if>
            <if test="adminId != null">ADMIN_ID = #{adminId},</if>
            <if test="adminName != null and adminName != ''">ADMIN_NAME = #{adminName},</if>
            <if test="adminPhone != null and adminPhone != ''">ADMIN_PHONE = #{adminPhone},</if>
            <if test="operatorId != null">OPERATOR_ID = #{operatorId},</if>
            <if test="operatorName != null and operatorName != ''">OPERATOR_NAME = #{operatorName},</if>
            <if test="operatorPhone != null and operatorPhone != ''">OPERATOR_PHONE = #{operatorPhone},</if>
            <if test="adminId != null and adminId != ''">ADMIN_ID = #{adminId},</if>
            <if test="adminName != null and adminName != ''">ADMIN_NAME = #{adminName},</if>
            <if test="adminPhone != null and adminPhone != ''">ADMIN_PHONE = #{adminPhone},</if>
            <if test="statusCd != null">STATUS_CD = #{statusCd},</if>
            <if test="statusDate != null">STATUS_DATE = #{statusDate},</if>
            <if test="createStaff != null">CREATE_STAFF = #{createStaff},</if>
            <if test="createDate != null">CREATE_DATE = #{createDate},</if>
            <if test="updateStaff != null">UPDATE_STAFF = #{updateStaff},</if>
            <if test="updateDate != null">UPDATE_DATE = #{updateDate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="unionPayMerchantCode != null and unionPayMerchantCode != ''">UNION_PAY_MERCHANT_CODE = #{unionPayMerchantCode},</if>
            <if test="payAccountId != null">PAY_ACCOUNT_ID = #{payAccountId},</if>
        </trim>
        where ZONE_ID = #{zoneId}
    </update>

    <delete id="deleteDigAssetZoneByZoneId" parameterType="Long">
        delete
        from dig_asset_zone
        where ZONE_ID = #{zoneId}
    </delete>

    <delete id="deleteDigAssetZoneByZoneIds" parameterType="String">
        delete from dig_asset_zone where ZONE_ID in
        <foreach item="zoneId" collection="array" open="(" separator="," close=")">
            #{zoneId}
        </foreach>
    </delete>
</mapper>