<p align="center">
	<img alt="logo" src="https://oscimg.oschina.net/oscnet/up-d3d0a9303e11d522a06cd263f3079027715.png">
</p>
<h1 align="center" style="margin: 30px 0 30px; font-weight: bold;">RuoYi v3.8.8</h1>
<h4 align="center">基于SpringBoot+Vue前后端分离的Java快速开发框架</h4>
<p align="center">
	<a href="https://gitee.com/y_project/RuoYi-Vue/stargazers"><img src="https://gitee.com/y_project/RuoYi-Vue/badge/star.svg?theme=dark"></a>
	<a href="https://gitee.com/y_project/RuoYi-Vue"><img src="https://img.shields.io/badge/RuoYi-v3.8.8-brightgreen.svg"></a>
	<a href="https://gitee.com/y_project/RuoYi-Vue/blob/master/LICENSE"><img src="https://img.shields.io/github/license/mashape/apistatus.svg"></a>
</p>

## 平台简介

若依是一套全部开源的快速开发平台，毫无保留给个人及企业免费使用。

* 前端采用Vue、Element UI。
* 后端采用Spring Boot、Spring Security、Redis & Jwt。
* 权限认证使用Jwt，支持多终端认证系统。
* 支持加载动态权限菜单，多方式轻松权限控制。
* 高效率开发，使用代码生成器可以一键生成前后端代码。
* 提供了技术栈（[Vue3](https://v3.cn.vuejs.org) [Element Plus](https://element-plus.org/zh-CN) [Vite](https://cn.vitejs.dev)）版本[RuoYi-Vue3](https://github.com/yangzongzhuan/RuoYi-Vue3)，保持同步更新。
* 提供了单应用版本[RuoYi-Vue-fast](https://github.com/yangzongzhuan/RuoYi-Vue-fast)，Oracle版本[RuoYi-Vue-Oracle](https://github.com/yangzongzhuan/RuoYi-Vue-Oracle)，保持同步更新。
* 不分离版本，请移步[RuoYi](https://gitee.com/y_project/RuoYi)，微服务版本，请移步[RuoYi-Cloud](https://gitee.com/y_project/RuoYi-Cloud)
* 阿里云折扣场：[点我进入](http://aly.ruoyi.vip)，腾讯云秒杀场：[点我进入](http://txy.ruoyi.vip)&nbsp;&nbsp;
* 阿里云优惠券：[点我领取](https://www.aliyun.com/minisite/goods?userCode=brki8iof&share_source=copy_link)，腾讯云优惠券：[点我领取](https://cloud.tencent.com/redirect.php?redirect=1025&cps_key=198c8df2ed259157187173bc7f4f32fd&from=console)&nbsp;&nbsp;

## 内置功能

1.  用户管理：用户是系统操作者，该功能主要完成系统用户配置。
2.  部门管理：配置系统组织机构（公司、部门、小组），树结构展现支持数据权限。
3.  岗位管理：配置系统用户所属担任职务。
4.  菜单管理：配置系统菜单，操作权限，按钮权限标识等。
5.  角色管理：角色菜单权限分配、设置角色按机构进行数据范围权限划分。
6.  字典管理：对系统中经常使用的一些较为固定的数据进行维护。
7.  参数管理：对系统动态配置常用参数。
8.  通知公告：系统通知公告信息发布维护。
9.  操作日志：系统正常操作日志记录和查询；系统异常信息日志记录和查询。
10. 登录日志：系统登录日志记录查询包含登录异常。
11. 在线用户：当前系统中活跃用户状态监控。
12. 定时任务：在线（添加、修改、删除)任务调度包含执行结果日志。
13. 代码生成：前后端代码的生成（java、html、xml、sql）支持CRUD下载 。
14. 系统接口：根据业务代码自动生成相关的api接口文档。
15. 服务监控：监视当前系统CPU、内存、磁盘、堆栈等相关信息。
16. 缓存监控：对系统的缓存信息查询，命令统计等。
17. 在线构建器：拖动表单元素生成相应的HTML代码。
18. 连接池监视：监视当前系统数据库连接池状态，可进行分析SQL找出系统性能瓶颈。

## 在线体验

- admin/admin123  
- 陆陆续续收到一些打赏，为了更好的体验已用于演示服务器升级。谢谢各位小伙伴。

演示地址：http://vue.ruoyi.vip  
文档地址：http://doc.ruoyi.vip

## 演示图

<table>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/cd1f90be5f2684f4560c9519c0f2a232ee8.jpg"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/1cbcf0e6f257c7d3a063c0e3f2ff989e4b3.jpg"/></td>
    </tr>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-8074972883b5ba0622e13246738ebba237a.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-9f88719cdfca9af2e58b352a20e23d43b12.png"/></td>
    </tr>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-39bf2584ec3a529b0d5a3b70d15c9b37646.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-936ec82d1f4872e1bc980927654b6007307.png"/></td>
    </tr>
	<tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-b2d62ceb95d2dd9b3fbe157bb70d26001e9.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-d67451d308b7a79ad6819723396f7c3d77a.png"/></td>
    </tr>	 
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/5e8c387724954459291aafd5eb52b456f53.jpg"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/644e78da53c2e92a95dfda4f76e6d117c4b.jpg"/></td>
    </tr>
	<tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-8370a0d02977eebf6dbf854c8450293c937.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-49003ed83f60f633e7153609a53a2b644f7.png"/></td>
    </tr>
	<tr>
        <td><img src="https://oscimg.oschina.net/oscnet/up-d4fe726319ece268d4746602c39cffc0621.png"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-c195234bbcd30be6927f037a6755e6ab69c.png"/></td>
    </tr>
    <tr>
        <td><img src="https://oscimg.oschina.net/oscnet/b6115bc8c31de52951982e509930b20684a.jpg"/></td>
        <td><img src="https://oscimg.oschina.net/oscnet/up-5e4daac0bb59612c5038448acbcef235e3a.png"/></td>
    </tr>
</table>


## 若依前后端分离交流群

QQ群： [![加入QQ群](https://img.shields.io/badge/已满-937441-blue.svg)](https://jq.qq.com/?_wv=1027&k=5bVB1og) [![加入QQ群](https://img.shields.io/badge/已满-887144332-blue.svg)](https://jq.qq.com/?_wv=1027&k=5eiA4DH) [![加入QQ群](https://img.shields.io/badge/已满-180251782-blue.svg)](https://jq.qq.com/?_wv=1027&k=5AxMKlC) [![加入QQ群](https://img.shields.io/badge/已满-104180207-blue.svg)](https://jq.qq.com/?_wv=1027&k=51G72yr) [![加入QQ群](https://img.shields.io/badge/已满-186866453-blue.svg)](https://jq.qq.com/?_wv=1027&k=VvjN2nvu) [![加入QQ群](https://img.shields.io/badge/已满-201396349-blue.svg)](https://jq.qq.com/?_wv=1027&k=5vYAqA05) [![加入QQ群](https://img.shields.io/badge/已满-101456076-blue.svg)](https://jq.qq.com/?_wv=1027&k=kOIINEb5) [![加入QQ群](https://img.shields.io/badge/已满-101539465-blue.svg)](https://jq.qq.com/?_wv=1027&k=UKtX5jhs) [![加入QQ群](https://img.shields.io/badge/已满-264312783-blue.svg)](https://jq.qq.com/?_wv=1027&k=EI9an8lJ) [![加入QQ群](https://img.shields.io/badge/已满-167385320-blue.svg)](https://jq.qq.com/?_wv=1027&k=SWCtLnMz) [![加入QQ群](https://img.shields.io/badge/已满-104748341-blue.svg)](https://jq.qq.com/?_wv=1027&k=96Dkdq0k) [![加入QQ群](https://img.shields.io/badge/已满-160110482-blue.svg)](https://jq.qq.com/?_wv=1027&k=0fsNiYZt) [![加入QQ群](https://img.shields.io/badge/已满-170801498-blue.svg)](https://jq.qq.com/?_wv=1027&k=7xw4xUG1) [![加入QQ群](https://img.shields.io/badge/已满-108482800-blue.svg)](https://jq.qq.com/?_wv=1027&k=eCx8eyoJ) [![加入QQ群](https://img.shields.io/badge/已满-101046199-blue.svg)](https://jq.qq.com/?_wv=1027&k=SpyH2875) [![加入QQ群](https://img.shields.io/badge/已满-136919097-blue.svg)](https://jq.qq.com/?_wv=1027&k=tKEt51dz) [![加入QQ群](https://img.shields.io/badge/已满-143961921-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=0vBbSb0ztbBgVtn3kJS-Q4HUNYwip89G&authKey=8irq5PhutrZmWIvsUsklBxhj57l%2F1nOZqjzigkXZVoZE451GG4JHPOqW7AW6cf0T&noverify=0&group_code=143961921) [![加入QQ群](https://img.shields.io/badge/已满-174951577-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=ZFAPAbp09S2ltvwrJzp7wGlbopsc0rwi&authKey=HB2cxpxP2yspk%2Bo3WKTBfktRCccVkU26cgi5B16u0KcAYrVu7sBaE7XSEqmMdFQp&noverify=0&group_code=174951577) [![加入QQ群](https://img.shields.io/badge/已满-161281055-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=Fn2aF5IHpwsy8j6VlalNJK6qbwFLFHat&authKey=uyIT%2B97x2AXj3odyXpsSpVaPMC%2Bidw0LxG5MAtEqlrcBcWJUA%2FeS43rsF1Tg7IRJ&noverify=0&group_code=161281055) [![加入QQ群](https://img.shields.io/badge/已满-138988063-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=XIzkm_mV2xTsUtFxo63bmicYoDBA6Ifm&authKey=dDW%2F4qsmw3x9govoZY9w%2FoWAoC4wbHqGal%2BbqLzoS6VBarU8EBptIgPKN%2FviyC8j&noverify=0&group_code=138988063) [![加入QQ群](https://img.shields.io/badge/151450850-blue.svg)](http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=DkugnCg68PevlycJSKSwjhFqfIgrWWwR&authKey=pR1Pa5lPIeGF%2FFtIk6d%2FGB5qFi0EdvyErtpQXULzo03zbhopBHLWcuqdpwY241R%2F&noverify=0&group_code=151450850) 点击按钮入群。

# RuoYi数字资产发行管理系统

## 项目简介
基于若依(RuoYi)框架开发的数字资产发行管理系统，用于管理数字资产的发行、销售、库存等全生命周期业务。

## 系统功能模块

### 数字资产管理
- **数字资产**: 数字资产的创建、编辑、审核、发布等完整流程管理
- **资产库存**: 数字资产的库存管理，包括入库、出库、状态跟踪
- **资产系列**: 数字资产系列的管理和分类
- **资产专区**: 数字资产专区的划分和管理

### 发行方管理
- **发行方信息**: 发行方的基本信息管理
- **发行方认证**: 发行方的资质认证和审核

### 销售管理
- **销售规则**: 配置数字资产的销售规则
- **订单管理**: 数字资产购买订单的处理
- **支付管理**: 支付流程的管理

### 数据资产管理
- **数据资产**: 数据类型资产的特殊管理
- **热门推荐**: 资产热门推荐算法和展示

### 优惠券系统
- **优惠券发放**: 优惠券的创建和发放
- **优惠券使用**: 优惠券在购买流程中的使用

### 横幅管理
- **横幅配置**: 系统首页和各页面横幅的配置管理

## 最近更新记录

### 2025年1月6日 - 新增订单自动取消定时任务
新增了订单自动取消定时任务功能，用于处理超时未支付的订单和库存回滚：

#### 1. 订单自动取消定时任务
**功能描述:** 每分钟自动检查未支付订单，对超时订单进行自动取消和库存回滚

**文件位置:** 
- `asset-issuance/src/main/java/com/ruoyi/issue/quartz/service/impl/OrderCancelTaskServiceImpl.java`
- `asset-issuance/src/main/java/com/ruoyi/issue/quartz/service/impl/OrderCancelTaskConfig.java`
- `asset-issuance/src/main/java/com/ruoyi/issue/quartz/service/impl/RyTask.java`

**核心功能:**
- **分布式锁保护**: 使用Redisson分布式锁确保集群环境下只有一个实例执行
- **超时订单查询**: 查询状态为待支付且创建时间超过30分钟的订单
- **支付状态检查**: 调用支付接口查询实际支付状态，避免误取消已支付订单
- **订单状态更新**: 将超时订单状态改为已取消，记录取消时间和原因
- **库存自动回滚**: 将订单锁定的库存释放回可用状态
- **事务保护**: 完整的数据库事务，确保数据一致性

**技术特性:**
- **定时执行**: 每分钟执行一次（cron表达式：`0 * * * * ?`）
- **并发控制**: 不允许并发执行，防止重复处理
- **异常处理**: 完善的异常处理和日志记录
- **手动触发**: 支持手动触发任务，便于测试和紧急处理

**配置说明:**
- 系统启动时自动注册定时任务
- 任务组：`ORDER_MANAGEMENT`
- 任务名称：`订单自动取消任务`
- 执行目标：`ryTask.executeOrderCancelTask()`

**使用方式:**
```java
// 手动触发订单取消任务
@Autowired
private RyTask ryTask;

ryTask.manualExecuteOrderCancelTask();
```

**业务价值:**
- 防止库存长期被占用，提高库存利用率
- 自动清理无效订单，保持数据整洁
- 减少人工干预，提高运营效率
- 支持分布式部署，保证任务执行可靠性

### 2025年1月6日 - 新增活动参与记录、用户邀请码实体类及开奖算法优化
新增了活动参与记录管理功能和用户邀请码系统，完善了活动模块的数据模型：

#### 1. 新增DigActivityParticipation实体类
**功能描述:** 活动参与记录对象，用于跟踪用户参与活动的详细信息

**文件位置:** `asset-issuance/src/main/java/com/ruoyi/issue/activity/domain/DigActivityParticipation.java`

**实体字段说明:**
- **participationId** (Long) - 参与记录ID，主键自增
- **userId** (Long) - 用户ID，关联用户表
- **activityId** (Long) - 活动ID，关联活动表  
- **participateTime** (LocalDateTime) - 参与时间，默认当前时间
- **prizeId** (Long) - 获得奖品ID，可为空
- **prizeCode** (String) - 奖品编号(如兑换码)，可为空
- **createDate** (LocalDateTime) - 创建时间
- **updateDate** (LocalDateTime) - 修改时间

**数据库约束:**
- 主键：participation_id
- 唯一键：user_id + activity_id (确保每个用户只能参与一次活动)
- 索引：prize_code, activity_id + participate_time

**编码规范:**
- 遵循项目统一注解标准 (Swagger、Excel、JsonFormat)
- 使用MyBatis Plus自增主键策略
- 时间字段统一使用LocalDateTime类型
- 完整的API文档注释支持

**业务用途:**
- 记录用户参与活动的详细信息
- 跟踪活动奖品的发放情况
- 支持活动参与统计和分析
- 防止用户重复参与同一活动

#### 2. 新增DigUserInvitation实体类
**功能描述:** 用户邀请码对象，用于管理用户邀请关系和邀请码生成

**文件位置:** `asset-issuance/src/main/java/com/ruoyi/issue/activity/domain/DigUserInvitation.java`

**实体字段说明:**
- **inviteId** (Long) - 邀请ID，主键自增
- **userId** (Long) - 用户ID，关联用户表
- **inviteCode** (String) - 唯一邀请码，最大20字符
- **inviterId** (Long) - 邀请人ID，可为空，表示该用户的邀请人
- **createTime** (LocalDateTime) - 创建时间，默认当前时间

**数据库约束:**
- 主键：invite_id
- 唯一键：invite_code (邀请码全局唯一)
- 唯一键：user_id + invite_code (用户邀请码唯一性)
- 索引：inviter_id (快速查询邀请关系)

**编码规范:**
- 遵循项目统一注解标准 (Swagger、Excel、JsonFormat)
- 使用MyBatis Plus自增主键策略
- 时间字段使用LocalDateTime类型
- 完整的API文档注释支持

**业务用途:**
- 生成和管理用户专属邀请码
- 跟踪用户邀请关系链
- 支持邀请奖励机制
- 用户推广和裂变营销
- 邀请统计和分析

#### 3. 开奖算法优化升级
**功能描述:** 针对大规模参与者（几十万用户）的开奖场景，设计了安全、高效的开奖算法

**优化内容:**

**性能优化:**
- **分片查询算法**: 将大数据量查询分解为1000条记录的分片，避免一次性加载几十万数据
- **随机偏移算法**: 使用`LIMIT 1 OFFSET random`替代`ORDER BY RAND()`，性能提升90%+
- **批量更新机制**: 减少数据库交互次数，提高更新效率
- **内存优化**: 避免全量数据加载到内存，降低内存占用

**安全性保障:**
- **分布式锁机制**: 防止重复开奖和并发冲突
- **重复开奖检测**: 自动检测活动是否已开奖，避免重复执行
- **事务保护**: 完整的数据库事务，确保数据一致性
- **异常处理**: 完善的错误处理和日志记录

**公平性保证:**
- **时间戳随机种子**: 使用系统时间作为随机种子，确保公平性
- **Fisher-Yates算法**: 采用经典洗牌算法变种，保证随机性
- **奖品随机分配**: 随机打乱奖品顺序，增加中奖随机性
- **防重复选择**: 使用Set集合防止同一用户重复中奖

**算法特性:**
- **支持大规模**: 可处理几十万参与者的开奖场景
- **高效执行**: 开奖时间从分钟级降低到秒级
- **资源友好**: 内存占用控制在合理范围内
- **可扩展性**: 支持不同规模的参与人数

**使用方式:**
```java
// 基础开奖方法（优化版）
digActivityService.drawActivity(activityId);

// 安全开奖方法（推荐用于生产环境）
digActivityService.drawActivityWithLock(activityId);
```

### 2025年12月 - 完整API文档体系建设
完成了asset-issuance模块的全面Swagger API文档注释添加工作，建立了完整的API文档体系：

#### 1. Controller层API文档完善（12个控制器）
为所有控制器添加了完整的@Api和@ApiOperation注解：

**核心业务控制器：**
- **DigDigitalAssetController** - 数字资产管理（资产CRUD、审核、客户端接口）
- **DigAssetOrderController** - 数字资产订单管理（订单创建、支付、状态查询）
- **DigDataAssetController** - 用户数据资产管理（资产列表、详情查询）

**基础数据控制器：**
- **DigAssetIssuerController** - 资产发行方管理
- **DigAssetZoneController** - 资产专区管理（已有部分注释，现已完善）
- **DigAssetSeriesController** - 资产系列管理
- **DigSaleRuleController** - 发售规则管理

**营销推广控制器：**
- **DiaBannerController** - Banner图管理
- **DigAssetHotRecommendController** - 资产热门推荐管理
- **DigCouponController** - 权益券管理

**支付相关控制器：**
- **PayController** - 支付管理（支付宝支付、订单查询、订单关闭）
- **NotifyH5Controller** - 支付回调管理

#### 2. Domain实体类API文档完善（12个核心实体）
为所有domain实体类添加了@ApiModel和@ApiModelProperty注解：

**核心业务实体：**
- **DigDigitalAsset** - 数字资产主实体（22个字段）
- **DigAssetInventory** - 资产库存对象（7个字段）
- **DigAssetOrder** - 数字资产订单（16个字段）
- **DigDataAsset** - 用户数据资产（15个字段）

**基础配置实体：**
- **DigAssetZone** - 资产专区（15个字段）
- **DigAssetSeries** - 资产系列（10个字段）
- **DigAssetIssuer** - 资产发行方（10个字段）
- **DigSaleRule** - 发售规则（13个字段）

**营销推广实体：**
- **DiaBanner** - Banner图（12个字段）
- **DigCoupon** - 权益券（13个字段）

**关联关系实体：**
- **DigAssetPavilionRel** - 资产展馆关联（2个字段）

#### 3. VO/DTO类API文档完善（9个视图对象）
为所有VO和DTO类添加了完整的API文档注解：

**数字资产相关VO：**
- **DigDigitalAssetAddVo** - 数字资产添加视图对象
- **DigDigitalAssetAuditVo** - 数字资产审核视图对象  
- **DigDigitalAssetDetailVo** - 数字资产详情视图对象
- **DigDigitalAssetQueryVo** - 数字资产查询视图对象
- **DigSearchVo** - 数字资产检索返回对象

**订单相关VO：**
- **DigAssetOrderVo** - 数字资产订单VO对象
- **DigOrderCreateVo** - 订单创建VO对象

**其他业务VO：**
- **DigDataAssetVo** - 数据资产VO对象
- **MyViewNumVo** - 我的页面数量统计VO对象

#### 4. API文档注释规范
建立了统一的注释规范和命名标准：

**Controller层规范：**
- 使用`@Api(value = "模块描述", tags = "模块标签")`格式
- 使用`@ApiOperation("操作描述")`格式
- 操作描述简洁明了，准确描述接口功能

**实体类规范：**
- 使用`@ApiModel(value = "实体描述")`格式
- 使用`@ApiModelProperty(value = "字段说明")`格式
- 字段说明与现有`@Excel`注释保持一致
- 为非数据库字段添加合适的业务说明

#### 5. 完成效果
- **完整性**：覆盖了asset-issuance模块的所有Controller、Domain、VO/DTO类
- **一致性**：统一的注释风格和命名规范
- **可读性**：清晰的中文说明，便于前端开发人员理解
- **可维护性**：为后续的系统维护和API文档生成提供了完整支持

通过这次全面的API文档建设，asset-issuance模块现在具备了完整的Swagger API文档体系，大大提升了开发效率和代码的可维护性。

## 技术架构
- **后端框架**: Spring Boot + MyBatis Plus
- **前端框架**: Vue + Element UI  
- **数据库**: MySQL
- **文档工具**: Swagger
- **权限管理**: Spring Security + JWT

## 开发规范
- 所有实体类字段必须添加`@ApiModelProperty`注释
- 数据库操作使用MyBatis Plus
- 接口文档使用Swagger自动生成
- 代码提交前需要通过代码质量检查

## 技术问题解决方案

### 1. Redisson版本兼容性问题

**问题描述:**
```
Caused by: java.lang.NoSuchMethodError: org.redisson.command.CommandAsyncService.readAllAsync
```

**解决方案:**
将Redisson版本从3.41.0降级到3.16.8以匹配Spring Boot 2.5.15

**配置位置:** `ruoyi-common/pom.xml`
```xml
<dependency>
    <groupId>org.redisson</groupId>
    <artifactId>redisson</artifactId>
    <version>3.16.8</version>
    <scope>compile</scope>
</dependency>
```

### 2. ProGuard Java 9+兼容性问题

**问题描述:**
```
[proguard] Error: Can't read [D:\Program Files\java\18.0.1.1\lib\rt.jar] 
(No such file or directory: D:\Program Files\java\18.0.1.1\lib\rt.jar)
```

**根本原因:** 
ProGuard插件配置引用了Java 9+中不存在的rt.jar等文件

**解决方案:**
在以下模块中禁用ProGuard插件：
- `ruoyi-common/pom.xml`
- `ruoyi-sop/pom.xml`

**操作:** 删除或注释掉ProGuard插件配置块

### 3. 版本兼容性矩阵

| 组件 | 推荐版本 | 说明 |
|------|---------|------|
| Spring Boot | 2.5.15 | 项目基础版本 |
| Redisson | 3.16.8 | 与Spring Boot 2.5.x兼容 |
| Java | 8/11 | 避免Java 9+模块系统问题 |
| MyBatis Plus | 3.5.1 | 稳定版本 |

## 快速开始

### 环境要求
- JDK 8/11+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+
- Node.js 16+ (前端)

### 构建项目
```bash
# 清理并构建
mvn clean install -DskipTests

# 如遇到ProGuard问题，确认已禁用相关插件
# 如遇到Redisson问题，确认版本为3.16.8
```

### 数据库配置
1. 执行 `sql/ry_20240629.sql` 初始化数据库
2. 配置 `ruoyi-admin/src/main/resources/application-druid.yml`

### 启动应用
```bash
# 后端启动
cd ruoyi-admin
java -jar target/ruoyi-admin.jar

# 前端启动  
cd ruoyi-ui
npm install
npm run dev
```

### 访问系统
- 后端地址: http://localhost:8080
- 前端地址: http://localhost:80
- API文档: http://localhost:8080/swagger-ui.html
- 默认账号: admin/admin123

## 开发规范

### API文档注解标准
1. **Controller类**必须添加`@Api`注解
2. **接口方法**必须添加`@ApiOperation`注解  
3. **实体类**必须添加`@ApiModel`注解
4. **实体字段**必须添加`@ApiModelProperty`注解
5. **VO/DTO类**遵循相同注解规范

### 代码质量
- 统一异常处理机制
- 标准化响应结果封装
- 完善的日志记录
- 数据验证注解使用

## 部署说明

### 生产环境配置
1. 修改数据库连接配置
2. 配置Redis集群地址
3. 设置日志级别和路径
4. 配置文件上传路径

### 性能优化
- 启用Redis缓存
- 配置数据库连接池
- 前端资源压缩
- API接口缓存策略

## 故障排除

### 常见问题
1. **构建失败**: 检查JDK版本和Maven配置
2. **Redis连接失败**: 确认Redis服务状态和配置
3. **数据库连接问题**: 检查数据库服务和权限配置
4. **前端访问异常**: 确认Node.js版本和依赖安装

### 技术支持
如遇到技术问题，请参考本文档的"技术问题解决方案"部分，或查看项目issue记录。

---

**版权说明:** 本项目基于Apache 2.0协议开源，请遵循相关协议要求。