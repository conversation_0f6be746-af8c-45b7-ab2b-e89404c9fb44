package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.DigUserInvitation;
import com.ruoyi.system.domain.vo.ActivityRankVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 活动Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface DigUserInvitationMapper extends BaseMapper<DigUserInvitation>
{

    @Select("select * from dig_user_invitation where <if test=\"inviteCode != null and inviteCode != ''\">  invite_code=#{inviteCode} </if>  <if test=\"userId != null and userId != ''\">user_id=#{userId} </if> ")
    DigUserInvitation getUserInvitation(String inviteCode, Long userId);


    List<ActivityRankVo> getInviteRank(@Param("startTime")  String startTime,@Param("endTime") String endTime);

    /**
     * 获取邀请排行榜（过滤指定前缀的用户）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param excludePrefixes 需要排除的用户名前缀列表
     * @return 邀请排行榜列表
     */
    List<ActivityRankVo> getInviteRankWithUserFilter(@Param("startTime") String startTime,
                                                     @Param("endTime") String endTime,
                                                     @Param("excludePrefixes") List<String> excludePrefixes);
}
