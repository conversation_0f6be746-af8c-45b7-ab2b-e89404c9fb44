<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.DigUserInvitationMapper">


    <select id="getInviteRank" resultType="com.ruoyi.system.domain.vo.ActivityRankVo">
        SELECT
        i.INVITER_ID AS 'userId',
        COUNT( i.INVITE_ID ) AS 'inviteNum'
        FROM
        `dig_user_invitation` AS i
        LEFT JOIN identify AS id ON i.USER_ID=id.USER_ID
        WHERE
        i.CREATE_DATE &gt;= '2025-07-28 18:00:00'
        AND i.CREATE_DATE &lt;= '2025-07-30 18:00:00'
        AND i.INVITER_ID IS NOT NULL
        AND id.USER_ID IS NOT NULL
        GROUP BY
        i.INVITER_ID
        ORDER BY
        inviteNum DESC
    </select>

    <select id="getInviteRankWithUserFilter" resultType="com.ruoyi.system.domain.vo.ActivityRankVo">
        SELECT
        dui.INVITER_ID AS 'userId',
        COUNT( dui.INVITE_ID ) AS 'inviteNum'
        FROM
        `dig_user_invitation` dui
        LEFT JOIN identify i ON dui.USER_ID = i.USER_ID
        WHERE
        dui.CREATE_DATE &gt;= #{startTime}
        AND dui.CREATE_DATE &lt;= #{endTime}
        AND dui.INVITER_ID IS NOT NULL
        AND i.ID_NAME IS NOT NULL
        <foreach collection="excludePrefixes" item="prefix" open="AND (" separator=" AND " close=")">
            i.PHONE NOT LIKE CONCAT(#{prefix}, '%')
        </foreach>
        GROUP BY
        dui.INVITER_ID
        ORDER BY inviteNum DESC
    </select>
</mapper>