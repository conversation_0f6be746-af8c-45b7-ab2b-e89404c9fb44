package com.ruoyi.sop.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.sop.dto.AgentPay;
import com.ruoyi.sop.dto.ChinaumsPayProperties;
import com.ruoyi.common.enums.ErrorEnum;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.uuid.IdUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RequiredArgsConstructor
@Slf4j
@Component
public class ChinaumsCommonService {

    private final ChinaumsPayProperties chinaumsPayProperties;

    @Autowired
    private RedisCache redisCache;


    /**
     * 计算签名
     * @param appId appId
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param appKey appkey
     * @return 签名结果
     */
    protected String createAuthSign(String appId,String timestamp, String nonce,String appKey) {
        return SecureUtil.sha256(appId+timestamp+nonce+appKey);
    }

    /**
     * 获取token，保存到redis
     * <AUTHOR>
     * @param tokenChannel token获取渠道（银联支付，银联实名认证）
     * @date 2022/9/20
     * @return
     **/
    private String getTokenFromChinaums(String tokenChannel){
        try {
            // 组装请求报文
            JSONObject authObject = new JSONObject();

            authObject.put("timestamp", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            authObject.put("nonce", IdUtils.fastSimpleUUID());
            authObject.put("signMethod","SHA256");
            // tokenChannel不为null，银联实名认证获取token，为null，银联支付获取token
            if(tokenChannel != null){
                authObject.put("appId",chinaumsPayProperties.getBankVerify().getAppId());
                authObject.put("signature",createAuthSign(chinaumsPayProperties.getBankVerify().getAppId(),authObject.getStr("timestamp"),authObject.getStr("nonce"),chinaumsPayProperties.getBankVerify().getAppKey()));
            }else {
                authObject.put("appId",chinaumsPayProperties.getAppId());
                authObject.put("signature",createAuthSign(chinaumsPayProperties.getAppId(),authObject.getStr("timestamp"),authObject.getStr("nonce"),chinaumsPayProperties.getAppKey()));
            }
            String result = HttpRequest.post(chinaumsPayProperties.getTokenUrl())
                    .header(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())
                    .body(authObject.toJSONString(0))
                    .timeout(3000)
                    .execute()
                    .body();
            JSONObject object = JSONUtil.parseObj(result);
            //code为0000 获取token成功，token有效时间为1个小时，这里设置55分钟过期失效
            if(object.getStr("errCode").equals("0000")){
                String token = object.getStr("accessToken");
                if(tokenChannel != null){
                    redisCache.setCacheObject(Constants.CHINA_UMS_BANK_TOKEN,token,55, TimeUnit.MINUTES);
                }else {
                    redisCache.setCacheObject(Constants.CHINA_UMS_TOKEN,token,55, TimeUnit.MINUTES);
                }
                return token;
            }else {
                log.error("获取token接口异常:{}",object.getStr("errInfo"));
                return null;
            }
        }catch (Exception e){
            log.error("获取token接口异常:{}",e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 从缓存中取token，若没有则保存
     * <AUTHOR>
     * @param tokenChannel 获取token渠道（银联支付，银联实名认证）
     * @date 2022/9/20
     **/
    public String getToken(String tokenChannel) {
        String token ;
        if(tokenChannel!=null){
            token = redisCache.getCacheObject(Constants.CHINA_UMS_BANK_TOKEN);
        }else {
            token = redisCache.getCacheObject(Constants.CHINA_UMS_TOKEN);
        }
        if(token == null){
            token = getTokenFromChinaums(tokenChannel);
            if(token == null){
                log.error("获取token接口异常");
                throw new ServiceException(ErrorEnum.SYSTEM_ERROR.getMsg());
            }
        }
        return token;
    }

    /**
     * 组装请求
     * <AUTHOR>
     * @param content 支付请求参数
     * @param url 支付地址
     * @date 2022/9/20
     **/
    public String createRequest(String content,String url){
        String token = getToken(null);
        String authorization = "OPEN-ACCESS-TOKEN AccessToken="+token;
        log.info("支付请求参数:{}",content);
        String result = null;
        try {
            result = HttpRequest.post(url)
                    .header(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())
                    .header("Authorization", authorization)
                    .body(content)
                    .timeout(3000)
                    .execute()
                    .body();
            log.info("支付返回的数据为：{}",result);
        }catch (Exception e){
            throw new ServiceException(ErrorEnum.PAY_REQUEST_ERROR.getMsg());
        }
        if (StrUtil.isBlank(result)){
            throw new ServiceException(ErrorEnum.PAY_REQUEST_ERROR.getMsg());
        }
        JSONObject object = JSONUtil.parseObj(result);
        if(!"0000".equals(object.getStr("respCode"))){
            log.error("支付失败:{}",result);
//            throw new ServiceException(ErrorEnum.CHINAUMS_ORDER_ERROR.getMsg());
        }
        return result;
    }


    /***
     * 查询可提现余额
     * @param chinaumsCustomerNumber
     * @return
     */
    public String queryWithdrawBalance(String chinaumsCustomerNumber) {
        String token = getToken(null);
        String authorization = "OPEN-ACCESS-TOKEN AccessToken="+token;
        JSONObject requestObject = new JSONObject();
        requestObject.put("sysId",chinaumsPayProperties.getAgentSysId());
//        log.info("sysId：{}",chinaumsPayProperties.getAgentSysId());
        requestObject.put("mchntNo",chinaumsCustomerNumber);
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        requestObject.put("reqTs",timestamp);
        requestObject.put("appType", "74");
        log.info("查询商户可提现金额参数：{}",requestObject);
        try{
            return HttpRequest.post(chinaumsPayProperties.getQueryWithdrawBalanceUrl())
                    .header(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())
                    .header("Authorization", authorization)
                    .body(JSONUtil.toJsonStr(requestObject))
                    .timeout(3000)
                    .execute()
                    .body();
        }catch (Exception e){
            log.error("商户号：{},查询商户可提现金额异常", chinaumsCustomerNumber, e);
             throw new ServiceException(ErrorEnum.QUERY_ORDER_ERROR.getMsg());
        }
    }

    /***
     * 处理提现
     * @param agentPay
     * @return
     */
    public String processWithdraw(AgentPay agentPay) {
        String token = getToken(null);
        String authorization = "OPEN-ACCESS-TOKEN AccessToken="+token;
        Map<String, String> param = new HashMap<>();
        param.put("sysId", chinaumsPayProperties.getAgentSysId());
        param.put("mchntNo", agentPay.getMerchantNo());
        param.put("appType", "74");
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        param.put("reqTs", timestamp);
        param.put("sysOrderId", agentPay.getOrderNo());
        param.put("withdrawType", "1");
        param.put("feeRatio", "0.01");
        param.put("subsFlag", "0");
        param.put("minFee", "0");
        param.put("withdrawAmt", agentPay.getWithdrawAmt());
        param.put("remark", "");
        log.info("提现参数：{}",param);
        try{
            return HttpRequest.post(chinaumsPayProperties.getProcessWithdrawUrl())
                    .header(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())
                    .header("Authorization", authorization)
                    .body(JSONUtil.toJsonStr(param))
                    .timeout(3000)
                    .execute()
                    .body();
        }catch (Exception e){
            log.error("订单号：{},提现异常", agentPay.getOrderNo(), e);
            throw new ServiceException(ErrorEnum.QUERY_ORDER_ERROR.getMsg());
        }
    }

    /***
     * 提现状态查询
     * @param agentPay
     * @return
     */
    public String qurProcessWithdraw(AgentPay agentPay) {
        String token = getToken(null);
        String authorization = "OPEN-ACCESS-TOKEN AccessToken="+token;
        Map<String, String> param = new HashMap<>();
        param.put("sysId", chinaumsPayProperties.getAgentSysId());
        param.put("mchntNo", agentPay.getMerchantNo());
        param.put("transDate", agentPay.getTransDate());
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        param.put("reqTs", timestamp);
        param.put("sysOrderId", agentPay.getOrderNo());
        log.info("提现状态查询参数：{}",param);
        try{
            return HttpRequest.post(chinaumsPayProperties.getQueryAgentPayStatusUrl())
                    .header(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_JSON.getMimeType())
                    .header("Authorization", authorization)
                    .body(JSONUtil.toJsonStr(param))
                    .timeout(3000)
                    .execute()
                    .body();
        }catch (Exception e){
            log.error("订单号：{},提现异常", agentPay.getOrderNo(), e);
            throw new ServiceException(ErrorEnum.QUERY_ORDER_ERROR.getMsg());
        }
    }
}
